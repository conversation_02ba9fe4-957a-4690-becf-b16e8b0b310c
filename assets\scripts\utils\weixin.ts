import { isInJglh, isInWeixin, isInWeApp } from './generalUtil';

// 移除url中指定参数
function getCurrentEnvName() {
  const envNames = {
    web: !isInJglh && !isInWeixin,
    jglh: isInJglh,
    weixin: isInWeixin && !isInWeApp,
    weapp: isInWeApp,
  };
  const currentEnv = Object.keys(envNames).filter(key => envNames[key])[0];
  return currentEnv || 'unknow';
}

/**
 * 清洗处理web页面url参数
 * @param {*} url
 */
export function handleJglhAndWeixinWebViewConfigParam(url) {
  try {
    if (window.URL && window.URLSearchParams) {
      let theURL = new URL(url);
      // lh_wvc参数用于原生app打开webview时使用，其他场景无用，此处移除掉
      theURL.searchParams.delete('lh_wvc');
      // 追加env参数，主要用于占位，规避微信支付无法调起问题
      theURL.searchParams.delete('env');
      theURL.searchParams.append('env', getCurrentEnvName());
      return theURL.href;
    }
  } catch (err) {
    console.error(err);
    return url;
  }
}

/**
 * 请求授权, `/actions/app/auth` 是另一个项目的授权中转页面，包含微信网页授权，云闪付小程序授权
 * @param {*} nextPage
 */
export function requestWeixinAuth(nextPage) {
  return new Promise((resolve, reject) => {
    const url = encodeURIComponent(
      handleJglhAndWeixinWebViewConfigParam(nextPage)
    );
    const query = ['auth_from=' + btoa(url), 't=' + Date.now()].join('&');
    // const host =
    location.href =
      `${location.protocol}//${location.hostname}` +
      '/actions/app/auth?' +
      query;
  });
}

/**
 * 请求授权, `/actions/app/auth` 是另一个项目的授权中转页面，包含微信网页授权，云闪付小程序授权
 * @param {*} nextPage
 */
export function requestWeixinAuthWithUid(nextPage, uid) {
  // return new Promise((resolve, reject) => {
  const url = encodeURIComponent(
    handleJglhAndWeixinWebViewConfigParam(nextPage)
  );
  const query = [
    'auth_from=' + btoa(url),
    't=' + Date.now(),
    'uid=' + btoa(uid),
  ].join('&');
  // const host =
  // location.href =
  //   `${location.protocol}//${location.hostname}` +
  //   '/actions/app/auth?' +
  //   query;
  const targetUrl =
    `${location.protocol}//${location.hostname}` + '/actions/app/auth?' + query;
  // resolve(targetUrl)
  return targetUrl;
  // });
}
// 设置分享信息
export function setWeixinShareInfo(options) {
  const shareInfo = {
    title: options.title,
    link: options.link,
    imgUrl: options.imgUrl || options.img,
    desc: options.desc || options.title, // 若无分享描述，则使用标题作为分享描述
    success: options.success || function () {},
    cancel: options.cancel || function () {},
  };
  const wx = window.wx;
  // 朋友圈没有分享描述，此处自动拼接到title上
  // const shareTimelineDesc = (shareInfo.title === shareInfo.desc || !shareInfo.desc) ? '' : ` | ${shareInfo.desc}`;
  // console.warn('shareTimelineDesc:', shareTimelineDesc);
  wx.onMenuShareAppMessage(shareInfo);
  wx.onMenuShareTimeline({ ...shareInfo, title: `${shareInfo.title}` });
  wx.onMenuShareQQ(shareInfo);
  wx.onMenuShareWeibo(shareInfo);
  wx.onMenuShareQZone(shareInfo);
}

export function hideWeixinMenus(menus) {
  const wx = window.wx;
  wx.hideMenuItems({
    menuList: menus,
  });
  /* return initWeiXinConfig(location.href).then(function(res) {
  }); */
}
