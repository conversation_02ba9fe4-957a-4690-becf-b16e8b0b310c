{"ver": "1.0.27", "uuid": "7cdc79dc-ed66-4b4c-b007-2cba70e16e5d", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nattribute vec3 a_position;\nattribute vec2 a_uv0;\nattribute vec4 a_color;\nvarying vec2 v_uv0;\nvarying vec4 v_color;\nvoid main () {\n  gl_Position = cc_matViewProj * vec4(a_position, 1);\n  v_uv0 = a_uv0;\n  v_color = a_color;\n}", "frag": "\nprecision highp float;\nvarying vec2 v_uv0;\nvarying vec4 v_color;\nuniform sampler2D texture;\nuniform vec2 center;\nuniform vec2 size;\nuniform float width;\nuniform float height;\nuniform float round;\nuniform float feather;\nvoid main () {\n  vec4 color = v_color;\n  color *= texture2D(texture, v_uv0);\n  float x = v_uv0.x;\n  float y = v_uv0.y;\n  float ratio = size.x / size.y;\n  float minX = center.x - (width / 2.0);\n  float maxX = center.x + (width / 2.0);\n  float minY = center.y - (height * ratio / 2.0);\n  float maxY = center.y + (height * ratio / 2.0);\n  if (x >= minX && x <= maxX && y >= minY && y <= maxY) {\n    if (round == 0.0) discard;\n    float roundY = round * ratio;\n    vec2 vertex;\n    if (x <= minX + round) {\n      if (y <= minY + roundY) {\n        vertex = vec2(minX + round, (minY + roundY) / ratio);\n      } else if (y >= maxY - roundY) {\n        vertex = vec2(minX + round, (maxY - roundY) / ratio);\n      } else {\n        vertex = vec2(minX + round, y / ratio);\n      }\n    } else if (x >= maxX - round) {\n      if (y <= minY + roundY){\n        vertex = vec2(maxX - round, (minY + roundY) / ratio);\n      } else if (y >= maxY - roundY) {\n        vertex = vec2(maxX - round, (maxY - roundY) / ratio);\n      } else {\n        vertex = vec2(maxX - round, y / ratio);\n      }\n    } else if (y <= minY + roundY) {\n      vertex = vec2(x, (minY + roundY) / ratio);\n    } else if (y >= maxY - roundY) {\n      vertex = vec2(x, (maxY - roundY) / ratio);\n    } else {\n      discard;\n    }\n    float dis = distance(vec2(x, y / ratio), vertex);\n    color.a = smoothstep(round - feather, round, dis) * color.a;\n  }\n  color.a *= v_color.a;\n  gl_FragColor = color;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nin vec3 a_position;\nin vec2 a_uv0;\nin vec4 a_color;\nout vec2 v_uv0;\nout vec4 v_color;\nvoid main () {\n  gl_Position = cc_matViewProj * vec4(a_position, 1);\n  v_uv0 = a_uv0;\n  v_color = a_color;\n}", "frag": "\nprecision highp float;\nin vec2 v_uv0;\nin vec4 v_color;\nuniform sampler2D texture;\nuniform Properties {\n  vec2 center;\n  vec2 size;\n  float width;\n  float height;\n  float round;\n  float feather;\n};\nvoid main () {\n  vec4 color = v_color;\n  color *= texture(texture, v_uv0);\n  float x = v_uv0.x;\n  float y = v_uv0.y;\n  float ratio = size.x / size.y;\n  float minX = center.x - (width / 2.0);\n  float maxX = center.x + (width / 2.0);\n  float minY = center.y - (height * ratio / 2.0);\n  float maxY = center.y + (height * ratio / 2.0);\n  if (x >= minX && x <= maxX && y >= minY && y <= maxY) {\n    if (round == 0.0) discard;\n    float roundY = round * ratio;\n    vec2 vertex;\n    if (x <= minX + round) {\n      if (y <= minY + roundY) {\n        vertex = vec2(minX + round, (minY + roundY) / ratio);\n      } else if (y >= maxY - roundY) {\n        vertex = vec2(minX + round, (maxY - roundY) / ratio);\n      } else {\n        vertex = vec2(minX + round, y / ratio);\n      }\n    } else if (x >= maxX - round) {\n      if (y <= minY + roundY){\n        vertex = vec2(maxX - round, (minY + roundY) / ratio);\n      } else if (y >= maxY - roundY) {\n        vertex = vec2(maxX - round, (maxY - roundY) / ratio);\n      } else {\n        vertex = vec2(maxX - round, y / ratio);\n      }\n    } else if (y <= minY + roundY) {\n      vertex = vec2(x, (minY + roundY) / ratio);\n    } else if (y >= maxY - roundY) {\n      vertex = vec2(x, (maxY - roundY) / ratio);\n    } else {\n      discard;\n    }\n    float dis = distance(vec2(x, y / ratio), vertex);\n    color.a = smoothstep(round - feather, round, dis) * color.a;\n  }\n  color.a *= v_color.a;\n  gl_FragColor = color;\n}"}}], "subMetas": {}}