// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import Request from '../apis/api';
import { getImageURL } from '../utils/generalUtil';
const { ccclass, property } = cc._decorator;

@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  rankBarContent: cc.Node = null;

  @property
  text: string = 'hello';

  // LIFE-CYCLE CALLBACKS:

  // onLoad () {}

  start() {
    cc.resources.load(
      'rankScene/prefab/userRankBar',
      cc.Prefab,
      (error, res: cc.Prefab) => {
        if (error) {
          console.log(error);
          return;
        }
        Request.getRankList({ max: 10 }).then((data: any) => {
          // console.log('排行榜', data);
          if (data?.length > 0) {
            let initY = -360;

            for (let i = 0; i < data.length; i++) {
              let node: cc.Node = cc.instantiate(res);

              node.parent = this.rankBarContent;
              let row =
                i == 0
                  ? this.rankBarContent.getChildByName('userRankBar1')
                  : i == 1
                  ? this.rankBarContent.getChildByName('userRankBar2')
                  : i == 2
                  ? this.rankBarContent.getChildByName('userRankBar3')
                  : node;
              // 远程 url 不带图片后缀名，此时必须指定远程图片文件的类型
              let remoteUrl = data[i].portrait
                ? getImageURL(data[i].portrait)
                : '';
              // let remoteUrl = getImageURL('FsNQq7LDPHFFyjDHSwfoMTR0RqYX');
              remoteUrl &&
                cc.assetManager.loadRemote(
                  remoteUrl,
                  { ext: '.png' },
                  function (err, texture) {
                    // 给Bar>image>img设置spriteFrame
                    if (err) {
                      console.error('加载远程图片失败:', err);
                      return;
                    }
                    if (texture instanceof cc.Texture2D) {
                      // 创建 SpriteFrame
                      let spriteFrame = new cc.SpriteFrame(texture);
                      row
                        .getChildByName('Bar')
                        .getChildByName('image')
                        .getChildByName('img')
                        .getComponent(cc.Sprite).spriteFrame = spriteFrame;
                    } else {
                      console.error('加载的资源不是有效的 Texture2D');
                    }
                  }
                );
              if (i < 3) {
                row.active = true;
                node.active = false;
              }
              // 隐藏row中的mask
              row.getChildByName('Bar').getChildByName('mask').active = false;
              row
                .getChildByName('Bar')
                .getChildByName('userName')
                .getComponent(cc.Label).string = data[i].nickName;

              row
                .getChildByName('Bar')
                .getChildByName('level')
                .getComponent(cc.Label).string = data[i].completedLevels;

              row
                .getChildByName('Bar')
                .getChildByName('time')
                .getComponent(cc.Label).string = data[i].totalTime + '秒';
              if (i > 2) {
                row
                  .getChildByName('Bar')
                  .getChildByName('rankNumber')
                  .getComponent(cc.Label).string = i + 1 + '';
                row.y = initY;
                initY = initY - 120;
              }
            }
          }
        });
      }
    );
  }

  // update (dt) {}
}
