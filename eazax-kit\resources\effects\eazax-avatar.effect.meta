{"ver": "1.0.27", "uuid": "52f68cfa-fef3-4f48-bafb-4ed5d9190d47", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\n#if USE_TEXTURE\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\nvarying vec4 v_color;\n#if USE_TEXTURE\nvarying vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nuniform float round;\nuniform float feather;\nfloat getAlpha(vec2 uv) {\n  vec2 vertex;\n  if (uv.x <= round) {\n    if (uv.y <= round) {\n      vertex = vec2(round, round);\n    } else if (uv.y >= 1.0 - round) {\n      vertex = vec2(round, (1.0 - round));\n    } else {\n      vertex = vec2(round, uv.y);\n    }\n  } else if (uv.x >= 1.0 - round) {\n    if (uv.y <= round){\n      vertex = vec2(1.0 - round, round);\n    } else if (uv.y >= 1.0 - round) {\n      vertex = vec2(1.0 - round, (1.0 - round));\n    } else {\n      vertex = vec2(1.0 - round, uv.y);\n    }\n  } else if (uv.y <= round) {\n    vertex = vec2(uv.x, round);\n  } else if (uv.y >= 1.0 - round) {\n    vertex = vec2(uv.x, (1.0 - round));\n  } else {\n    vertex = uv;\n  }\n  float dis = distance(uv, vertex);\n  return smoothstep(round, round - feather, dis);\n}\nvoid main () {\n  vec4 color = v_color;\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture2D(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    color.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    color.a *= texture_tmp.a;\n  #else\n    color *= texture_tmp;\n  #endif\n  #endif\n  if (color.a == 0.0) {\n    discard;\n  }\n  if (round > 0.0) {\n    color.a *= getAlpha(v_uv0);\n  }\n  gl_FragColor = color;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\n#if USE_TEXTURE\nin vec2 a_uv0;\nout vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\nin vec4 v_color;\n#if USE_TEXTURE\nin vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nuniform Properties {\n  float round;\n  float feather;\n};\nfloat getAlpha(vec2 uv) {\n  vec2 vertex;\n  if (uv.x <= round) {\n    if (uv.y <= round) {\n      vertex = vec2(round, round);\n    } else if (uv.y >= 1.0 - round) {\n      vertex = vec2(round, (1.0 - round));\n    } else {\n      vertex = vec2(round, uv.y);\n    }\n  } else if (uv.x >= 1.0 - round) {\n    if (uv.y <= round){\n      vertex = vec2(1.0 - round, round);\n    } else if (uv.y >= 1.0 - round) {\n      vertex = vec2(1.0 - round, (1.0 - round));\n    } else {\n      vertex = vec2(1.0 - round, uv.y);\n    }\n  } else if (uv.y <= round) {\n    vertex = vec2(uv.x, round);\n  } else if (uv.y >= 1.0 - round) {\n    vertex = vec2(uv.x, (1.0 - round));\n  } else {\n    vertex = uv;\n  }\n  float dis = distance(uv, vertex);\n  return smoothstep(round, round - feather, dis);\n}\nvoid main () {\n  vec4 color = v_color;\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    color.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    color.a *= texture_tmp.a;\n  #else\n    color *= texture_tmp;\n  #endif\n  #endif\n  if (color.a == 0.0) {\n    discard;\n  }\n  if (round > 0.0) {\n    color.a *= getAlpha(v_uv0);\n  }\n  gl_FragColor = color;\n}"}}], "subMetas": {}}