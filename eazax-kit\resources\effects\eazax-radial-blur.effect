// 径向模糊
// 20211202
// https://gitee.com/ifaswind/eazax-ccc/blob/master/resources/effects/eazax-radial-blur.effect

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        center: { value: [0.5, 0.5], editor: { tooltip: '模糊中心点' } }
        strength: { value: 0.5, editor: { tooltip: '模糊强度' } }
        texture: { value: white }
        alphaThreshold: { value: 0.5 }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>

  in vec3 a_position;
  in vec4 a_color;
  out vec4 v_color;

  #if USE_TEXTURE
  in vec2 a_uv0;
  out vec2 v_uv0;
  #endif

  void main () {
    vec4 pos = vec4(a_position, 1);

    #if CC_USE_MODEL
    pos = cc_matViewProj * cc_matWorld * pos;
    #else
    pos = cc_matViewProj * pos;
    #endif

    #if USE_TEXTURE
    v_uv0 = a_uv0;
    #endif

    v_color = a_color;

    gl_Position = pos;
  }
}%


CCProgram fs %{
  precision highp float;

  #include <alpha-test>
  #include <texture>

  in vec4 v_color;

  #if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D texture;
  #endif

  uniform Properties {
    vec2 center;
    float strength;
  };

  #if USE_TEXTURE
  // 获取径向模糊颜色
  vec4 getRadialBlurColor(vec2 coord) {
      // 偏移
	    vec2 offset = coord.xy - center;
      // 采样次数
      const int samples = 10;
      // 强度
      float perStrength = strength / float(samples - 1);
      // 采样
      vec4 color = vec4(0.0);
      for(int i = 0; i < samples; i++) {
          vec2 uv = offset * (1.0 + (float(i) * perStrength)) + center;
          color += texture2D(texture, uv);
      }
      color /= float(samples);
      // 完成
      return color;
  }
  #endif

  void main () {
    vec4 o = vec4(1, 1, 1, 1);

    #if USE_TEXTURE
      o *= getRadialBlurColor(v_uv0);
    #endif

    o *= v_color;

    ALPHA_TEST(o);

    #if USE_BGRA
      gl_FragColor = o.bgra;
    #else
      gl_FragColor = o.rgba;
    #endif
  }
}%
