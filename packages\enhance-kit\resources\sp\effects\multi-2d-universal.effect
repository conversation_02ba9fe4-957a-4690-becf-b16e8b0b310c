// Copyright (c) 2017-2018 Xiamen Yaji Software Co., Ltd.  

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        texture: { value: white }
        texture2: { value: white }
        texture3: { value: white }
        texture4: { value: white }
        texture5: { value: white }
        texture6: { value: white }
        texture7: { value: white }
        texture8: { value: white }
        alphaThreshold: { value: 0.5 }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>
  
  in vec3 a_position;
  in vec4 a_color;
  out vec4 v_color;

  #if USE_TINT
  in vec4 a_color0;
  out vec4 v_color0;
  #endif

  #if USE_TEXTURE
  in vec2 a_uv0;
  out vec2 v_uv0;

  #if USE_MULTI_TEXTURE
  in float a_texId;
  out float v_texId;
  #endif

  #endif

  void main () {
    vec4 pos = vec4(a_position, 1);

    #if CC_USE_MODEL
    pos = cc_matViewProj * cc_matWorld * pos;
    #else
    pos = cc_matViewProj * pos;
    #endif

    #if USE_TEXTURE
    v_uv0 = a_uv0;
    
    #if USE_MULTI_TEXTURE
    v_texId = a_texId;
    #endif

    #endif

    v_color = a_color;
    #if USE_TINT
    v_color0 = a_color0;
    #endif

    gl_Position = pos;
  }
}%


CCProgram fs %{
  precision highp float;
  
  #include <alpha-test>
  #include <texture>

  in vec4 v_color;

  #if USE_TINT
  in vec4 v_color0;
  #endif

  #if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D texture;

  #if USE_MULTI_TEXTURE 
  in float v_texId;
  uniform sampler2D texture2;
  uniform sampler2D texture3;
  uniform sampler2D texture4;
  uniform sampler2D texture5;
  uniform sampler2D texture6;
  uniform sampler2D texture7;
  uniform sampler2D texture8;
  #endif
  
  #endif

  void main () {
    vec4 o = vec4(1, 1, 1, 1);

    #if USE_TEXTURE
      #if USE_MULTI_TEXTURE 
        if(v_texId < 1.0){
          CCTexture(texture, v_uv0, o);
        } else if(v_texId < 2.0){
          CCTexture(texture2, v_uv0, o);
        } else if(v_texId < 3.0){
          CCTexture(texture3, v_uv0, o);
        } else if(v_texId < 4.0){
          CCTexture(texture4, v_uv0, o);
        } else if(v_texId < 5.0){
          CCTexture(texture5, v_uv0, o);
        } else if(v_texId < 6.0){
          CCTexture(texture6, v_uv0, o);
        } else if(v_texId < 7.0){
          CCTexture(texture7, v_uv0, o);
        } else {
          CCTexture(texture8, v_uv0, o);
        }
      #else
        CCTexture(texture, v_uv0, o);
      #endif
    #endif

    #if USE_TINT
      vec4 finalColor;
      finalColor.a = v_color.a * o.a;
      finalColor.rgb = ((o.a - 1.0) * v_color0.a + 1.0 - o.rgb) * v_color0.rgb + o.rgb * v_color.rgb;

      ALPHA_TEST(finalColor);

      gl_FragColor = finalColor;
    #else
      o *= v_color;

      ALPHA_TEST(o);

      gl_FragColor = o;
    #endif
  }
}%
