// 单色
// 20201124
// https://gitee.com/ifaswind/eazax-ccc/blob/master/resources/effects/eazax-single-color.effect

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>

  in vec3 a_position;
  in vec2 a_uv0;
  in vec4 a_color;

  out vec2 v_uv0;
  out vec4 v_color;

  void main () {
    gl_Position = cc_matViewProj * vec4(a_position, 1);
    v_uv0 = a_uv0;
    v_color = a_color;
  }
}%


CCProgram fs %{
  precision highp float;

  in vec2 v_uv0;
  in vec4 v_color;

  uniform sampler2D texture;

  void main () {
    vec4 t_color = texture2D(texture, v_uv0);
    gl_FragColor = vec4(v_color.r, v_color.g, v_color.b, t_color.a);
  }
}%
