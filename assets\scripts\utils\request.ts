// request.ts
import { getFingerprint } from './storage';
import { login, getSession, getSystemInfo } from './bridge';
import { isInWeApp, isInWeixin, isInJGLH, isPlainObject } from './generalUtil';

type RequestMethod = 'GET' | 'POST' | 'JSON';
type RequestOptions = {
  onAuthorityFail?: (msg: string) => Promise<never>;
};

// 辅助函数保持不变
function preSerialize(obj: any) {
  if (isPlainObject(obj)) {
    return Object.keys(obj).reduce((previous: Record<string, any>, key) => {
      const item = obj[key];
      previous[key] = isPlainObject(item) ? String(item) : item;
      return previous;
    }, {});
  }
  return obj;
}

function onAuthorityFail(msg: string) {
  // dialog('提示').confirm(msg, {
  //   okText: '去登录',
  //   ok() {
  //     login({ force: true });
  //   },
  //   cancel() {
  //     // popWebView(0);
  //   },
  // });
  return Promise.reject('登录失效');
}
function stringify(
  obj: Record<string, any>,
  options?: { arrayFormat?: 'repeat' | 'bracket' }
): string {
  const str: string[] = [];
  const { arrayFormat = 'bracket' } = options || {}; // 默认使用 'bracket'

  for (const [key, value] of Object.entries(obj)) {
    if (Array.isArray(value)) {
      if (arrayFormat === 'repeat') {
        // 使用 'repeat' 格式，将每个值都作为单独的键值对
        for (const item of value) {
          str.push(encodeURIComponent(key) + '=' + encodeURIComponent(item));
        }
      } else {
        // 默认使用 'bracket' 格式，将数组的值转换为 key[] 的形式
        for (const item of value) {
          str.push(
            encodeURIComponent(key + '[]') + '=' + encodeURIComponent(item)
          );
        }
      }
    } else if (typeof value === 'object' && value !== null) {
      // 对于嵌套对象，进行递归处理
      for (const nestedKey in value) {
        str.push(
          encodeURIComponent(key + '[' + nestedKey + ']') +
            '=' +
            encodeURIComponent(value[nestedKey])
        );
      }
    } else {
      // 单个键值对
      str.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));
    }
  }

  return str.join('&');
}
export function serialize(data: any) {
  return stringify(preSerialize(data), { arrayFormat: 'repeat' });
}

function isAuthorityError(data: any) {
  return (
    data &&
    data.code &&
    (/^6\d+/.test(data.code) || data.code == 4003 || data.code == 20001)
  );
}

// 创建 XMLHttpRequest 的 Promise 封装
function xhrRequest(
  method: string,
  url: string,
  data: any,
  headers: Record<string, string>
): Promise<any> {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    let requestData: string | null = null;
    // 如果url是http开头，那么就是请求的url，否则就加上origin前缀
    let requestUrl = url.startsWith('http')
      ? url
      : `${window.location.origin}${url}`;
    if (CC_DEBUG) {
      requestUrl = url.startsWith('http')
        ? url
        : `http://192.168.3.153:7457/api${url}`;
    }

    // 处理 GET 参数
    if (method === 'GET' && data) {
      const params = serialize(data);
      requestUrl += (url.includes('?') ? '&' : '?') + params;
    } else if (method === 'POST') {
      requestData = data ? serialize(data) : null;
      headers['Content-Type'] = 'application/x-www-form-urlencoded';
    } else if (method === 'JSON') {
      requestData = data ? JSON.stringify(data) : null;
      headers['Content-Type'] = 'application/json';
    }

    xhr.open(method === 'JSON' ? 'POST' : method, requestUrl, true);
    xhr.timeout = 30000;
    xhr.withCredentials = true;

    // 设置请求头
    Object.keys(headers).forEach(key => {
      xhr.setRequestHeader(key, headers[key]);
    });

    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch (e) {
          reject('Invalid JSON response');
        }
      } else {
        reject(xhr.statusText);
      }
    };

    xhr.onerror = () => {
      reject('Network Error');
    };

    xhr.ontimeout = () => {
      reject('Request Timeout');
    };

    xhr.send(requestData);
  });
}

// 创建请求头（保持原有逻辑）
function createHeaders() {
  let deviceNo = getFingerprint();
  if (isInJGLH) {
    getSystemInfo().then((res: any) => {
      if (res?.id) deviceNo = res.id;
    });
  }

  const headers: Record<string, string> = {
    'device-no': deviceNo,
    'jglh-agent': navigator.userAgent,
  };

  const scene = new URLSearchParams(window.location.search).get('scene');
  if (scene) headers.scene = scene;

  return headers;
}

// 处理请求逻辑
function doRequest(
  method: RequestMethod,
  url: string,
  data: any,
  onAuthFail?: (msg: string) => Promise<never>
) {
  return new Promise(async (resolve, reject) => {
    const headers = createHeaders();
    let source = '';
    let platform = '';

    // 平台判断逻辑保持不变
    if (isInWeApp) {
      platform = 'XCX';
      headers.xcxtoken = getSession();
    } else {
      platform = isInWeixin ? 'WEIXIN' : isInJGLH ? 'APP' : '';
      headers.authority = getSession();
    }

    headers.source = source;
    headers.platform = platform;

    try {
      const response = await xhrRequest(method, url, data, headers);
      if (!response.code || response.code === 200) {
        resolve(response.data ?? response);
        return;
      }

      if (isAuthorityError(response)) {
        const tip = headers.authority
          ? '您的登录会话已失效'
          : '请登录后再进行后续操作';
        login({ force: true });
        reject(tip);
        return;
      }

      throw {
        code: response.code,
        message: response.msg || '请求失败',
      };
    } catch (err) {
      let message = err.message;
      let code = err.code || 0;

      if (err?.code) {
        // 处理已知错误类型
        if (err.code === 503) {
          reject('服务正在升级中，请稍后再试');
        } else if (err.code === 502) {
          reject('当前服务暂不可用，请稍后再试');
        }
      }

      throw {
        code,
        message,
      };
    }
  });
}

// 对外暴露的方法保持不变
export function doGet(api: string, data?: any, options: RequestOptions = {}) {
  return doRequest('GET', api, data, options.onAuthorityFail);
}

export function doPost(api: string, data?: any, options: RequestOptions = {}) {
  return doRequest('POST', api, data, options.onAuthorityFail);
}

export function postJSON(
  api: string,
  data?: any,
  options: RequestOptions = {}
) {
  return doRequest('JSON', api, data, options.onAuthorityFail);
}

// 保持原有辅助函数不变（handleRequestURL、addQuery、onAuthorityFail等）
