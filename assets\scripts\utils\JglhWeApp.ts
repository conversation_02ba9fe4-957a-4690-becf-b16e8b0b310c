// JglhWeApp.ts

// 声明 wx 类型（确保你的项目已引入微信 JS SDK 类型声明文件，或手动声明 wx 对象的类型）
declare var wx: {
  miniProgram: {
    navigateTo: (options: { url: string }) => void;
    redirectTo: (options: { url: string }) => void;
    navigateBack: () => void;
  };
};

interface OpenMiniProgramOptions {
  type?: 'navigate' | 'redirect'; // 跳转类型
  url?: string; // 跳转地址
  query?: string; // 传递的参数
}

interface PaymentInfo {
  oid: string; // 系统订单号
  success: string; // 支付成功回跳地址
  fail: string; // 支付失败回跳地址
  category: string; // 订单类型
}

/**
 * 跳转到登录页面
 * 登录成功后采用跳转刷新页面的方式
 * @param {string} backurl 回跳地址
 */
function login(backurl?: string): void {
  wx.miniProgram.navigateTo({
    url:
      '/pages/login/index?backurl=' +
      encodeURIComponent(backurl || location.href),
  });
}

/**
 * 跳转到小程序页面
 * @param {OpenMiniProgramOptions} options 跳转配置
 */
function openMiniProgramPage(options: OpenMiniProgramOptions): void {
  const _options: OpenMiniProgramOptions = Object.assign(
    {
      type: 'navigate', // 或redirect, 默认navigate
      url: '/pages/index/index', // 跳转页面地址, 默认首页
      query: '', // 传递参数 a=1&b=2
    },
    options
  );

  const urlWithQuery =
    _options.url + (_options.query ? '?' + _options.query : '');

  if (_options.type === 'navigate') {
    wx.miniProgram.navigateTo({
      url: urlWithQuery,
    });
  } else {
    wx.miniProgram.redirectTo({
      url: urlWithQuery,
    });
  }
}

/**
 * 调起小程序支付页面
 * @param {PaymentInfo} info 支付信息
 */
function payment(info: PaymentInfo): void {
  const page =
    '/pages/payment/index?orderId=' +
    info.oid +
    '&success=' +
    encodeURIComponent(info.success) +
    '&fail=' +
    encodeURIComponent(info.fail) +
    '&category=' +
    encodeURIComponent(info.category);

  wx.miniProgram.navigateTo({
    url: page,
  });
}

/**
 * 打开新窗口
 * @param {string} url 要打开的url地址
 */
function pushWebview(url: string): void {
  wx.miniProgram.navigateTo({
    url: '/pages/web/index?url=' + encodeURIComponent(url),
  });
}

// 关闭当前窗口
function closeWebview(): void {
  wx.miniProgram.navigateBack();
}

// 导出模块
export default {
  closeWebview,
  pushWebview,
  payment,
  toLogin: login, // 兼容老代码调用，暂不删除
  login,
  openMiniProgramPage,
};
