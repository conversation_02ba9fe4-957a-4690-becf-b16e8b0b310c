// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import { authLogin } from '../utils/generalUtil';

@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  loginBtn: cc.Node = null;

  @property(cc.Node)
  loginDialog: cc.Node = null;

  duration: number = 0.5;

  // LIFE-CYCLE CALLBACKS:

  //

  maskBlackInAction() {
    return cc.tween().to(this.duration, { opacity: 255, scale: 1 });
  }

  protected onEnable(): void {
    this.loginDialog.opacity = 0;
    this.loginDialog.scale = 1.2;

    cc.tween(this.loginDialog).then(this.maskBlackInAction()).start();
  }

  onLoad() {}

  authLogin() {
    this.node.active = false;
    authLogin();
  }

  onLoginBtnClick() {
    this.node.active = false;
  }
  onDestroy() {}
}
