import compareVersions from './compareVersions';
import Utils from '../utils';
import ENV from '../env';

// jglh-js-sdk 兼容表
// https://developers.weixin.qq.com/miniprogram/dev/api/base/wx.canIUse.html
const compatible = {
  setSoftInput: {
    $version: '>= 3.9.3',
  },
  startDiscriminateVoice: {
    $version: '>= 4.2.0',
  },
  watchDiscriminateVoice: {
    $version: '>= 4.2.0',
  },
  endDiscriminateVoice: {
    $version: '>= 4.2.0',
  },
  uploadRecord: {
    $version: '>= 3.7.0',
    callback: {
      success: '>=4.2.0',
    },
  },
  pay: {
    $version: '>= 3.7.0',
    callback: {
      success: '>= 4.2.0',
      fail: '>= 4.2.0',
    },
    object: {
      'data.channel.union_pay_mobile': '>= 4.2.0',
    },
  },
  login: {
    $version: '>= 3.7.0',
    callback: {
      success: '>= 4.2.0',
    },
  },
  getSystemInfo: {
    $version: '>= 4.3.0',
    callback: {
      success: '> 3.9.x',
    },
  },
  pushNativeView: {
    $version: '>= 4.3.0',
    object: {
      'id.user_profile': '>= 4.3.0',
    },
  },
  chooseVideo: {
    $version: '>= 4.4.0',
  },
  playVideo: {
    $version: '>= 4.4.0',
  },
  chooseLocation: {
    $version: '>= 4.4.0',
  },
  openLocation: {
    $version: '>= 4.4.0',
  },
  getLocation: {
    $version: '>= 4.4.0',
  },
  toSoundForce: {
    $version: '>= 4.5.5',
  },
  openDidi: {
    $version: '>= 4.5.6',
  },
  saveImage: {
    $version: '>= 4.6.0',
  },
};

/**
 *
 * @param {string} compatibleVersion 兼容版本
 * @param {string} currentVersion 当前版本
 */
function compareVersion(compatibleVersion, currentVersion) {
  const [symbol, targetVersion] = compatibleVersion.split(' ');
  return compareVersions.compare(currentVersion, targetVersion, symbol);
}

// 从兼容列表中获取指定方法或参数的兼容版本
function getCompatibleVersion(key) {
  try {
    const result = compatible[key];
    if (Utils.isPlainObject(result)) {
      return result.$version;
    } else if (Utils.isString(result)) {
      return result;
    } else throw new Error('invalid data structure');
  } catch {
    return null;
  }
}

// 从 userAgent中获取的版本号，格式为 数值类型，这种格式在a.b.c版本号规范中，出现两位数版本时会有问题，目前是尽可能避免两位数版本号
function getCurrentVersion() {
  const version = String(Utils.getVersion());
  return version.split('').join('.');
}

export default {
  /**
   * 判断方法或参数是否可用，借鉴了小程序的canIUse方法
   * https://developers.weixin.qq.com/miniprogram/dev/api/base/wx.canIUse.html
   * 鉴于目前，app端相关代码的不可控性，兼容判断采用了前端维护一个兼容性对象来实现
   * 更好的的实现方案，应该是调用app端的兼容判断代码来判断
   * @param {string} schema
   */
  canIUse(schema) {
    if (!ENV.jglh) return false;
    const result = getCompatibleVersion(schema);
    if (!result) return true;
    let currentVersion = getCurrentVersion();
    return compareVersion(result, currentVersion);
  },
};
