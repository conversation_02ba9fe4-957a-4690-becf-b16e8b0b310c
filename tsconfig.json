{
  "compilerOptions": {
    "module": "CommonJS",
    "allowJs": true,          // 允许导入 JS 文件
    "lib": [ "es2015", "es2017", "dom" ],
    "target": "es5",
    "downlevelIteration": true, // 启用这个选项
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "outDir": "temp/vscode-dist",
    "forceConsistentCasingInFileNames": true
  },
  "include": [  
    "assets/**/*.ts", // 确保包含所有 ts 文件
    "creator-sp.d.ts",
    "creator.d.ts",
    "global.d.ts", // 自定义声明文件
  ],
  "exclude": [
    "node_modules",
    "library",
    "local",
    "temp",
    "build",
    "settings",
    "assets/scripts/jglhSdk/**/*.ts", // 确保包含所有 ts 文件
    "assets/scripts/jglhSdk/*.ts"
  ]
}