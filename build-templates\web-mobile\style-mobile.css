html {
  -ms-touch-action: none;
}

body, canvas, div {
  display: block;
  outline: none;
  -webkit-tap-highlight-color: rgba(89, 67, 255, 0);

  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  -webkit-tap-highlight-color: rgba(89, 67, 255, 0);
}

/* Remove spin of input type number */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  /* display: none; <- Crashes Chrome on hover */
  -webkit-appearance: none;
  margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

body {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  border: 0;
  margin: 0;

  cursor: default;
  color: #888;
  background-color: #5943FF;

  text-align: center;
  font-family: Helvetica, Verdana, Arial, sans-serif;

  display: flex;
  flex-direction: column;
  
  /* fix bug: https://github.com/cocos-creator/2d-tasks/issues/791 */
  /* overflow cannot be applied in Cocos2dGameContainer, 
  otherwise child elements will be hidden when Cocos2dGameContainer rotated 90 deg */
  overflow: hidden;
}

#Cocos2dGameContainer {
  position: absolute;
  margin: 0;
  left: 0px;
  top: 0px;

  display: -webkit-box;
  -webkit-box-orient: horizontal;
  -webkit-box-align: center;
  -webkit-box-pack: center;
}

canvas {
  background-color: #5943FF;
}

a:link, a:visited {
  color: #666;
}

a:active, a:hover {
  color: #666;
}

p.header {
  font-size: small;
}

p.footer {
  font-size: x-small;
}

#splash {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #171717 url(./splash.85cfd.png) no-repeat center;
  background-size: 45%;
}

.progress-bar {
    position: absolute;
    left: 27.5%;
    top: 80%;
    height: 3px;
    padding: 2px;
    width: 45%;
    border-radius: 7px;
    box-shadow: 0 1px 5px #000 inset, 0 1px 0 #444;           
}

.progress-bar span {
    display: block;
    height: 100%;
    border-radius: 3px;
    transition: width .4s ease-in-out;
    background-color: #3dc5de;
}

.stripes span {
    background-size: 30px 30px;
    background-image: linear-gradient(135deg, rgba(255, 255, 255, .15) 25%, transparent 25%,
                        transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%,
                        transparent 75%, transparent);            
    
    animation: animate-stripes 1s linear infinite;             
}

@keyframes animate-stripes {
    0% {background-position: 0 0;} 100% {background-position: 60px 0;}
}

#GameCanvas{
  visibility: hidden;
}
#preload {
  width: 100vw;
  height: 100vh;
  position: absolute;
  left: 0;
  top: 0;
}
.preload-content{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.loader {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
}

.dot {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #ffffff;
  border-radius: 50%;
  /* transform-origin: center; */
  animation: wave 1.2s ease-in-out infinite both;
}
@keyframes wave {
  0%, 100% {
    transform: translate(-50%, -50%) rotate(var(--rotate)) translateY(-30px) scale(0.5);
  }
  50% {
    transform: translate(-50%, -50%) rotate(var(--rotate)) translateY(-30px) scale(1);
  }
}

/* 调整 animation-delay 的顺序为从大到小 */
.dot:nth-child(1) { --rotate: 0deg; animation-delay: -1.2s; }
.dot:nth-child(2) { --rotate: 30deg; animation-delay: -1.1s; }
.dot:nth-child(3) { --rotate: 60deg; animation-delay: -1.0s; }
.dot:nth-child(4) { --rotate: 90deg; animation-delay: -0.9s; }
.dot:nth-child(5) { --rotate: 120deg; animation-delay: -0.8s; }
.dot:nth-child(6) { --rotate: 150deg; animation-delay: -0.7s; }
.dot:nth-child(7) { --rotate: 180deg; animation-delay: -0.6s; }
.dot:nth-child(8) { --rotate: 210deg; animation-delay: -0.5s; }
.dot:nth-child(9) { --rotate: 240deg; animation-delay: -0.4s; }
.dot:nth-child(10) { --rotate: 270deg; animation-delay: -0.3s; }
.dot:nth-child(11) { --rotate: 300deg; animation-delay: -0.2s; }
.dot:nth-child(12) { --rotate: 330deg; animation-delay: -0.1s; }

#progress{
  text-align: center;
  color: #ffffff;
}