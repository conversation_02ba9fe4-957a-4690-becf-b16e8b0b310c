import configs from '../config';
import Utils from '../utils';
import { parseJglhURL } from './jglh-url-parser';

const jglhVersion = Utils.getVersion();

export default {
  /**
   * 分享信息设置
   * @alias module:native.setShareConfig
   * @param {object} option
   * @param {string} option.title - 分享标题
   * @param {string} option.link - 分享链接
   * @param {string} option.imgUrl - 分享图标
   * @param {function} option.success - 分享成功回调
   * @param {function} option.cancel - 取消分享回调
   */
  setShareConfig: function (option) {
    var that = this;
    // debugger
    // var settings = that.fnFilter(option);
    that.callNative('setShareConfig', option);
  },

  /**
   * 虚拟键盘控制
   * @alias module:native.setSoftInput
   * @param {object} option
   * @param {string} option.status  - 键盘控制属性 auto: 根据页面焦点事件弹起键盘   popup：主动弹起键盘
   * @param {string} option.before  - 键盘开始回调事件
   * @param {string} option.success - 键盘抬起成功回调
   * @param {function} option.hidden   - 键盘收起
   */
  setSoftInput: function (option) {
    var that = this;
    // var settings = that.fnFilter(option, true);
    that.callNative('setSoftInput', option);
  },

  /**
   * 检测是否是调试模式
   * @alias module:native.isDebug
   * @param {debugCallback} callback - 获得成功回调函数
   */
  isDebug: function (callback) {
    var that = this;

    var cb = function (value) {
      var isDebugMode = value === 'yes' || value === true;
      return callback(isDebugMode);
    };

    that.callNative('isDebug', null, cb);
  },

  /**
   * 获取用户UID
   * @alias module:native.getUID
   * @param {getUIDCallback} callback - 获取用户UID后的回调函数
   */
  getUID: function (callback) {
    var that = this;

    function cb(uid) {
      var theUID = uid;
      if (theUID) {
        if (/^u/.test(theUID) || theUID.length > 32) theUID = null;
      }
      callback(theUID);
    }
    that.callNative('getUID', null, cb);
  },

  /**
   * 获取用户sessionId
   * @alias module:native.getSession
   * @param {callback} callback - 获取用户session后的回调函数
   */
  getSession: function (callback) {
    var that = this;
    that.callNative('getUserSession', null, callback);
  },

  /**
   * 调用活动详情
   * @alias module:native.showDetail
   * @param {object} data - 一大堆信息一股脑塞进去了
   */
  showDetail: function (data) {
    var that = this;
    that.callNative('showDetail', data);
  },
  /**
   * 设置webview导航栏标题
   * @param {*} data
   */
  setTitle: function (data) {
    var that = this;
    that.callNative('setTitle', data);
  },

  /**
   * 打开一个新窗口
   * @param {Object} options : Object
   * @param {String} options.url: 打开的页面的url
   * @param {String} options.title: 打开的页面的初始标题，默认为空字符串
   * @param {Boolean} options.titleBar : 是否需要标题栏，默认true
   * @param {Boolean} options.pulldownRefresh: 是否需要下拉刷新，默认true
   * @param {Boolean} options.autoUpdateTitle: 是否自动设置标题，默认true，true时页面每次加载完自动将标题设为页面的title
   * @param {Boolean} options.progressBar: 是否显示加载进度
   * @param {Boolean} options.shareButton: 是否显示分享按钮
   */
  pushWebView: function (options) {
    this.callNative('pushWebview', options);
  },
  /**
   * @callback getSystemInfoSuccessCallback
   * @param {object} options
   * @param {string} options.id 设备唯一标识
   * @param {string} options.brand 设备品牌
   * @param {string} options.model 设备型号
   * @param {number} options.pixelRatio 设备像素比
   * @param {number} options.screenWidth 屏幕宽度，单位px
   * @param {number} options.screenHeight 屏幕高度，单位px
   * @param {number} options.windowWidth 可使用窗口宽度，单位px
   * @param {number} options.windowHeight 可使用窗口高度，单位px
   * @param {number} options.statusBarHeight 状态栏的高度，单位px
   * @param {string} options.language 语言
   * @param {string} options.version 交广领航版本号，版本号格式 Major.Minor.Patch（主版本号.次版本号.修订版本号)
   * @param {string} options.system 操作系统及版本
   * @param {string} options.platform 客户端平台，android|ios
   * @param {string} options.SDKVersion JS-SDK版本，从交广领航4.3.0开始，为js-sdk增加一个独立的版本号，采用语义化版本号规范，从2.2.0开始
   */
  /**
   * 获取系统信息
   * @param {object} options
   * @param {getSystemInfoSuccessCallback} options.success - 成功回调
   * @param {function} options.fail - 失败回调
   */
  getSystemInfo: function (options) {
    this.callNative('getSystemInfo', options);
  },
  /**
   * 获取APP设置信息-用户个性化配置
   * @param {object} options
   * @param {getSystemInfoSuccessCallback} options.success - 成功回调
   * @param {function} options.fail - 失败回调
   */
  getAppSettingInfo: function (options) {
    this.callNative('getAppSettingInfo', options);
  },
  /*
   * 关闭窗口
   * @param {Number} length:退出当前窗口，目前只传0
   */
  popWebView: function (length) {
    this.callNative('popWebview', length);
  },

  /*
   * 清除webview的浏览记录，若无法实现可不实现
   */
  clearHistory: function () {
    this.callNative('clearHistory', null);
  },
  /**
   * 分享信息
   * @param {object} option
   * @param {string} option.title - 分享标题
   * @param {string} option.link - 分享链接
   * @param {string} option.img - 分享图标
   * @param {function} option.success - 分享成功回调
   * @param {function} option.cancel - 取消分享回调
   */
  share: function (option) {
    this.callNative('share', option);
  },

  /*
   * 一个通用的打开一个native端界面的方法
   * @param {Object} options: 参数对象
   * @param {String} options.id: 打开的窗口的标志
   *        options.id traffic_map: 打开路况界面
   *        options.id set_payment_password: 打开设置支付密码的界面
   *        options.id notification_of_order: 打开订单通知消息的界面
   *        options.id user_profile: 打开个人资料页面 , v4.3新增
   * @param {Function} options.callback: 退出此界面时的回调函数，调用时传入webview页面需要的参数，没有不传
   *
   */
  pushNativeView: function (options) {
    // var settings = this.fnFilter(options);
    // console.info('settings', settings);
    this.callNative('pushNativeView', options);
  },

  /*
   * 调出登录界面
   * @alias module:native.login
   * @param {function} callback - 登录成功的回调函数
   */
  login: function (callback) {
    var that = this;
    // 4.2.0版本开始，ios端调起登录界面的方法名增加 `login`
    if (jglhVersion && jglhVersion >= 420) {
      that.callNative('login', {
        success: callback,
      });
    } else {
      that.callNative({
        ios: 'appJsLogin',
        android: 'login',
      });
      window.loginSuccess = callback;
    }
  },

  /*
   * 调用支付方法
   * @alias module:native.pay
   * @param {object} option
   * @param {object} option.data - ping++支付信息
   * @param {function} option.paySuccess - 支付成功回调函数
   * @param {function} option.payFail - 支付失败回调函数
   */
  pay: function (option) {
    var that = this;
    var jglhVersion = Utils.getVersion();
    // 交广领航4.2.0以后版本，支付方法统一为pay；成功失败回调函数动态传入
    if (jglhVersion && jglhVersion < 420) {
      window.paySuccess = option.success;
      window.payFailed = option.fail;
      that.callNative('pay', option.data);
    } else {
      // const settings = this.fnFilter(option);
      that.callNative('pay', option);
    }
  },

  /*
   * 扫描二维码
   * @alias module:native.scanQR
   * @param {object} option
   */
  scanQR: function () {
    var that = this;
    that.callNative('codeScan');
  },
  /**
   * 注册pushWebView方法打开的webview的回调函数，页面刷新后自动解除所有注册的事件
   * 触发条件：当webview的某些事件被触发时
   * @param {Object} option
   * @param {String} option.event 事件名称，当前只有一个back
   * @param {Boolean} option.preventDefault 阻止原生默认事件，默认false
   * @paramBody {String} option.event<back> 后退事件，当webview将要后退时触发
   * @param {Function} option.callback 事件回调函数
   */
  addWebViewListener: function (option) {
    // var settings = this.fnFilter(option, true);
    this.callNative('addWebViewListener', option);
  },

  /**
   * 导航
   * @param {Object} option 导航参数
   * @param {String} option.address 目的地名称
   * @param {Number} option.longitude 目的地经度
   * @param {Number} option.latitude 目的地维度
   * @param {Function} option.callback 退出导航界面回调函数
   */
  navigate: function (option) {
    // var settings = this.fnFilter(option, true);
    this.callNative('navigate', option);
  },
  openWeex: function (url) {
    this.callNative('openWeex', url);
  },
  getGeoData: function (callback) {
    var cb = function (geo) {
      if (geo) callback(JSON.parse(geo.toLowerCase()));
      else callback(null);
    };
    this.callNative('getGeoData', null, cb);
  },
  callTellphone: function (data) {
    this.callNative('callTellphone', data);
  },
  toEmergencySchedule: function (data) {
    console.log('commonjs-------', JSON.stringify(data, null, 4));
    this.callNative('toEmergencySchedule', data);
  },
  getVersion: function () {
    return Utils.getVersion();
  },
  /**
   * android端控制下拉刷新是否开启
   * @param {Object} flag
   */
  setPulldownRefreshFlag: function (flag) {
    if (window.appJs && window.appJs.setPulldownRefreshFlag)
      window.appJs.setPulldownRefreshFlag(flag ? 0 : 1);
  },
  /**
   * android端设置进度条状态：0：禁用，1:启用
   */
  setProcessBarStatus: function (value) {
    if (window.appJs && window.appJs.setProcessBarStatus)
      window.appJs.setProcessBarStatus(value);
  },
  /**
   * android端设置是否自动更新标题状态：0：禁用自动更新，1:启用
   */
  setAutoUpdateTitleStatus: function (value) {
    if (window.appJs && window.appJs.setAutoUpdateTitleStatus)
      window.appJs.setAutoUpdateTitleStatus(value);
  },
  showNewsDetail: function (data) {
    this.callNative('showNewsDetail', data);
  },

  couldUseNative: function () {
    return (
      window[configs.nativeCallee.android] || window[configs.nativeCallee.ios]
    );
  },

  canGoBack: function (callback) {
    var that = this;

    function cb(value) {
      callback(value);
    }
    that.callNative('canGoBack', null, cb);
  },
  /**
   * @description: 打开有声书
   * @return {*}
   */
  toSoundForce: function () {
    var that = this;
    that.callNative('toSoundForce');
  },
};
