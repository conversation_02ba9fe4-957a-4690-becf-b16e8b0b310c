{"ver": "1.0.27", "uuid": "df8f581a-99d1-4888-8b15-1433dca8bf6c", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nattribute vec3 a_position;\nattribute vec2 a_uv0;\nattribute vec4 a_color;\nvarying vec2 v_uv0;\nvarying vec4 v_color;\nvoid main () {\n  gl_Position = cc_matViewProj * vec4(a_position, 1);\n  v_uv0 = a_uv0;\n  v_color = a_color;\n}", "frag": "\nprecision highp float;\nvarying vec2 v_uv0;\nvarying vec4 v_color;\nuniform sampler2D texture;\nuniform vec2 center;\nuniform float ratio;\nuniform float radius;\nuniform float feather;\nvoid main () {\n  vec4 color = v_color;\n  color *= texture2D(texture, v_uv0);\n  if (color.a == 0.0) discard;\n  float dis = distance(vec2(v_uv0.x, v_uv0.y / ratio), vec2(center.x, center.y / ratio));\n  color.a = smoothstep(radius, radius - feather, dis);\n  color.a *= v_color.a;\n  gl_FragColor = color;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nin vec3 a_position;\nin vec2 a_uv0;\nin vec4 a_color;\nout vec2 v_uv0;\nout vec4 v_color;\nvoid main () {\n  gl_Position = cc_matViewProj * vec4(a_position, 1);\n  v_uv0 = a_uv0;\n  v_color = a_color;\n}", "frag": "\nprecision highp float;\nin vec2 v_uv0;\nin vec4 v_color;\nuniform sampler2D texture;\nuniform Properties {\n  vec2 center;\n  float ratio;\n  float radius;\n  float feather;\n};\nvoid main () {\n  vec4 color = v_color;\n  color *= texture(texture, v_uv0);\n  if (color.a == 0.0) discard;\n  float dis = distance(vec2(v_uv0.x, v_uv0.y / ratio), vec2(center.x, center.y / ratio));\n  color.a = smoothstep(radius, radius - feather, dis);\n  color.a *= v_color.a;\n  gl_FragColor = color;\n}"}}], "subMetas": {}}