// Eazax-CCC 镂空(矩形) 1.0.1.20200604
// https://gitee.com/ifaswind/eazax-ccc/blob/master/resources/effects/eazax-hollowout-rect.effect

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        size: { value: [500.0, 500.0], editor: { tooltip: '节点尺寸' } }
        center: { value: [0.5, 0.5], editor: { tooltip: '中心点 (左上角为原点)' } }
        width: { value: 0.25, editor: { tooltip: '宽 (目标宽度 / 节点宽度)' } }
        height: { value: 0.25, editor: { tooltip: '高 (目标高度 / 节点宽度)' } }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>

  in vec3 a_position;
  in vec2 a_uv0;
  in vec4 a_color;

  out vec2 v_uv0;
  out vec4 v_color;

  void main () {
    gl_Position = cc_matViewProj * vec4(a_position, 1);
    v_uv0 = a_uv0;
    v_color = a_color;
  }
}%


CCProgram fs %{
  precision highp float;

  in vec2 v_uv0;
  in vec4 v_color;

  uniform sampler2D texture;

  uniform Properties {
    vec2 center;
    vec2 size;
    float width;
    float height;
  };

  void main () {
    vec4 color = v_color;
    color *= texture(texture, v_uv0);

    float ratio = size.x / size.y;
    float minX = center.x - (width / 2.0);
    float maxX = center.x + (width / 2.0);
    float minY = center.y - (height * ratio / 2.0);
    float maxY = center.y + (height * ratio / 2.0);
    if (v_uv0.x >= minX && v_uv0.x <= maxX && v_uv0.y >= minY && v_uv0.y <= maxY) {
      discard;
    }

    color.a *= v_color.a;
    gl_FragColor = color;
  }
}%
