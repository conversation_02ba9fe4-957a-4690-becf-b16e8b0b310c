// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import { ActionUtils } from '../utils/ActionUtils';
import { globalVariables } from '../utils/GlobalVariables';

@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Label)
  label: cc.Label = null;

  @property(cc.ScrollView)
  scrollView: cc.ScrollView = null;

  @property(cc.Node)
  maskSuccess: cc.Node = null;

  @property(cc.Node)
  kindTips: cc.Node = null;

  @property
  text: string = 'hello';

  @property
  knowledgeIndex: number = 0;

  duration: number = 0.8;

  // LIFE-CYCLE CALLBACKS:

  backBtn() {
    const scrollView = this.scrollView;
    scrollView.scrollToTop();
    this.node.active = false;

    // 关闭时复位滚动位置
    // 数量统计当前的被点击过的函数
    // globalVariables.currentFoundElement++;
    this.isFindAll();
  }

  // 判断是否找完
  isFindAll() {
    let shouldFoundElement =
      globalVariables.LevelShouldFoundElementArray[
        globalVariables.currentLevel - 1
      ];
    if (globalVariables.currentFoundElement == shouldFoundElement) {
      // 如果通关了，则更换handleBtn的图片为lottery
      if (
        globalVariables.currentLevel == 4 ||
        (globalVariables.passLevelArray[3] == 1 &&
          globalVariables.passTimeArray[3] != -1 &&
          globalVariables.passTimeArray[3] != 0)
      ) {
        cc.resources.load(
          'buttons/lottery',
          cc.SpriteFrame,
          (err, spriteFrame) => {
            if (err) {
              cc.error('加载图片失败: ' + err);
              return;
            }
            // 使用 spriteFrame
            this.maskSuccess
              .getChildByName('handleBtn')
              .getComponent(cc.Sprite).spriteFrame =
              spriteFrame as cc.SpriteFrame;
            // this.maskSuccess.active = true;
            this.showKindTips();
          }
        );
        return;
      }
      // setTimeout(() => {
      // this.maskSuccess.active = true;
      this.showKindTips();
      // }, 1100);
    }
  }
  showKindTips() {
    let pngNum = globalVariables.currentLevel;
    cc.resources.load(
      'messageBar/tip_' + pngNum,
      cc.SpriteFrame,
      (err, spriteFrame) => {
        if (err) {
          cc.error('加载图片失败: ' + err);
          return;
        }
        // 使用 spriteFrame
        this.kindTips
          .getChildByName('tips')
          .getComponent(cc.Sprite).spriteFrame = spriteFrame as cc.SpriteFrame;
        this.kindTips.active = true;
      }
    );
  }
  onLoad() {}

  protected onEnable(): void {
    // this.scrollView.scrollToTop();
    this.node.scale = 0.5;
    this.node.opacity = 100;
    cc.tween(this.node)
      .then(ActionUtils.restoreScaleAndOpacityAction(this.duration))
      .start();

    let level = globalVariables.currentLevel - 1;
    // let current = globalVariables.currentFoundElement;
    // 设置maskMessage的text属性
    this.text =
      globalVariables.LevelKnowledgeData[level][this.knowledgeIndex].content;
    this.label.string = this.text;
  }

  start() {}

  // update (dt) {}
}
