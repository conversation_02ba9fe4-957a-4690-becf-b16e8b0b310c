// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import { globalVariables } from '../utils/GlobalVariables';

@ccclass
export default class SceneTouchElement extends cc.Component {
  @property(cc.Label)
  label: cc.Label = null;

  @property
  text: string = 'hello';

  currentLevelElementData =
    globalVariables.LevelElementData[globalVariables.currentLevel - 1];
  currentLevelKnowledgeData =
    globalVariables.LevelKnowledgeData[globalVariables.currentLevel - 1];

  // LIFE-CYCLE CALLBACKS:

  onLoad() {}

  showTips() {
    let childrens = this.node.children;

    childrens.forEach(element => {
      element.getChildByName('tips').active = true;
    });
  }

  start() {
    // 每一关需要展示的element都不一样，从globalVariables.LevelShouldFoundElementArray中获取,不需要展示的element隐藏，并不要加载贴图
    let shouldFoundElement =
      globalVariables.LevelShouldFoundElementArray[
        globalVariables.currentLevel - 1
      ];
    let children: cc.Node[] = this.node.children;

    // console.log(children);
    // 根据shouldFoundElement的值，隐藏不需要展示的element，并不要加载贴图

    children.forEach((element, index) => {
      // console.log(index);
      if (index >= shouldFoundElement) {
        element.active = false;
        element.getComponent(cc.Sprite).spriteFrame = null;
        // 跳出循环
        return;
      }
      // 1. 设置节点位置
      element.x = this.currentLevelElementData[index].position[0];
      element.y = this.currentLevelElementData[index].position[1];

      // 2. 加载贴图
      if (!this.currentLevelElementData[index].size) {
        cc.resources.load(
          this.currentLevelElementData[index].resourcesURL,
          cc.SpriteFrame,
          null,
          (err, spriteFrame) => {
            if (err) {
              cc.error('贴图加载失败: ' + err);
              return;
            }
            element.getComponent(cc.Sprite).spriteFrame =
              spriteFrame as cc.SpriteFrame;
          }
        );
        let tips = element.getChildByName('tips');
        // 设置tips的位置
        tips.x = -element.width / 3;
        tips.y = 0;
        let explainBg = element.getChildByName('explainBg');
        if (explainBg) {
          let textLabel = explainBg.getChildByName('explainText');
          //   text是个label，设置label的string
          textLabel.getComponent(cc.Label).string =
            this.currentLevelKnowledgeData[index].tips;
        }
      } else {
        element.width = this.currentLevelElementData[index].size[0];
        element.height = this.currentLevelElementData[index].size[1];

        // 设置图片透明
        element.getComponent(cc.Sprite).spriteFrame = null;
      }
      element['elementIndex'] = index;
      // console.log('图片加载完成');
    });
  }

  // update (dt) {}
}
