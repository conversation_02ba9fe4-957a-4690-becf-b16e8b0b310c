module.exports = {
    'COCOS_CREATE_EXTENSION': '创建新扩展插件...',
    'primary_menu': '社区版管理...',
    'settings_menu': '社区版设置...',
    'install_menu': '安装...',
    'info_menu': '查看信息',
    'uninstall_menu': '卸载',
    'last_menu': '安装最新版本',
    'other_menu': '安装其它版本...',
    'doc_menu': '文档',
    'website_menu': '官方网站',
    'github_menu': 'Github',
    'info_title': 'Cocos Enhance Kit 信息',
    'info_title2': '已安装社区版信息',
    'engine_version_title': '当前引擎版本：',
    'error_engine_version_not_support': '当前引擎版本不支持安装社区版',
    'support_version_title': '支持的社区版版本：',
    'not_install': '请先安装社区版。',
    'version_not_2_0': '安装的社区版版本需 >= 2.0.0 ，以支持设置面板功能。',
    'error_use_global': '项目自定义引擎正在使用全局配置，无法读取到已安装的社区版信息',
    'uninstall': '未安装',
    'install_success': '安装成功',
    'install_failed': '安装失败',
    'uninstall_success': '卸载成功',
    'uninstall_failed': '卸载失败，请重试',
    'not_backup': '备份丢失导致无法卸载，需要重装引擎以卸载',
    'restart_tip': '安装成功，请重启编辑器生效',
    'failed_tip': '安装失败，请检查错误并重试',
    'restart_tip2': '卸载成功，请重启编辑器生效',
    'failed_tip2': '卸载失败，请检查错误并重试',
    'uninstalling': '正在卸载当前安装的社区版，请勿操作...',
    'use_global_tip': '项目自定义引擎正在使用全局配置，无法自动修改，请手动还原全局配置',
    'skip': '不存在，已跳过安装',
    'thank': '感谢你对 Cocos Enhance Kit 开源项目的支持。',
    'settings_title': '社区版设置',
    'unsupport_version_1': '社区版版本 ',
    'unsupport_version_2': ' 不能在此版本引擎上安装',
    'check_version_prefix': '正在检查社区版目录，版本 ',
    'install_version_prefix': '正在安装社区版，版本 ',
    'dont_action': '，请勿操作...',
    'tip1': '如果出现自动下载或者解压失败的问题，请再重试几次。',
    'tip2': '若多次重试失败，可在以下网址手动下载 zip 文件，并放在',
    'tip3': '目录解压。',
    'no_version': '本地不存在该版本社区版，开始下载...',
    'downloading': '正在下载中...',
    'delete_dir_ing': '正在删除旧目录...',
    'unziping': '正在解压...',
    'unzip_failed': '解压失败，可能是压缩包损坏，已将压缩包删除，请重新再试',
    'loading': '加载中...',
    'thread_not_right_workers_dir': '你启用了社区版的多线程特性，但未检测到正确的 workers 目录与 game.json 字段，请重新安装社区版，详情请查看文档：https://smallmain.github.io/cocos-enhance-kit/docs/user-guide/multithread/thread-intro#%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9',
    'thread_need_delete_files': '你禁用了社区版的多线程特性，可以手动删除相关文件以减少微信小游戏的包体大小，详情请查看文档：https://smallmain.github.io/cocos-enhance-kit/docs/user-guide/multithread/thread-intro#%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9',
    'thread_title': '多线程支持',
    'thread_desc': '该特性仅在微信小游戏平台下有效。',
    'thread_desc2': '请注意，以下为全局设置，改动会影响到所有项目，并且在重新安装、升级或卸载社区版后丢失所有设置。',
    'thread_debug': '调试模式',
    'thread_debug_desc': '启用后将会输出详细日志以便进行调试，这可能会大幅降低性能。',
    'thread_custom': '项目多线程扩展',
    'thread_custom_desc': '启用后将会激活项目 worker 目录下的自定义扩展。',
    'thread_http': '多线程驱动 XMLHttpRequest',
    'thread_http_desc': '启用后 XMLHttpRequest 将会移至线程中执行，由于存在数据往返的耗时，请实际测试对性能是否有提升。',
    'thread_asset_pipeline': '多线程驱动资源管线',
    'thread_asset_pipeline_desc': '启用后将资源管线移至线程中执行，减少由资源下载、缓存与加载导致的卡顿。',
    'thread_audio_system': '多线程驱动音频系统',
    'thread_audio_system_desc': '启用后将音频耗时操作移至线程中执行，减少由音频 API 调用导致的卡顿。',
    'thread_audio_sync': '属性同步间隔（毫秒）',
    'thread_audio_sync_desc': '间隔多久从 Worker 线程将音频实例的属性（播放进度、总时长等）同步到主线程，太频繁可能会影响性能。',
    'thread_ws': '多线程驱动 WebSocket',
    'thread_ws_desc': '启用后将 WebSocket 移至线程中执行，并且允许自定义数据编解码，由于存在数据往返的耗时，请实际测试对性能是否有提升。',
    'thread_subpackage': '设为小游戏子包',
    'thread_subpackage_desc': '启用后将 workers 目录设为小游戏子包，这会减少主包的大小，但可能影响启动性能，请视情况启用。',
    'thread_scheduler': '线程通信调度器',
    'thread_scheduler_desc': '启用后将多次数据通信打包发送，这可能会减少因通信次数带来的性能消耗。',
    'thread_compile_custom_thread_menu': '重新编译多线程扩展',
    'thread_create_custom_thread_menu': '项目多线程扩展',
    'thread_custom_not_exists_1': '你启用了社区版的项目多线程扩展，但项目中不存在 worker 目录，可以通过依次点击菜单项 ',
    'thread_custom_not_exists_2': ' 来创建扩展。',
    'create_thread_custom_success': '已在 worker 目录创建项目多线程扩展。',
    'refresh_thread_custom_success': '已重新编译多线程扩展。',
    'create_thread_custom_already_exists': '项目已存在 worker 目录，仅更新 creator-worker.d.ts 至最新版本。',
    'thread_custom_need_delete': '项目中存在多线程扩展，若不再需要可手动删除项目中的 worker 目录。',
};
