// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import { AppConstants } from './utils/constants';
import { $_setShareInfo } from './utils/generalUtil';
import { globalVariables } from './utils/GlobalVariables';

@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  rankBG: cc.Node = null;

  @property(cc.Node)
  backBtn: cc.Node = null;

  @property(cc.Node)
  rankBarComponent: cc.Node = null;

  @property
  text: string = 'hello';

  duration: number = 0.8;

  onLoad() {
    // 获取背景图，确保适应屏幕
    const bgSprite = this.rankBG.getComponent(cc.Sprite);
    bgSprite.node.width = cc.view.getVisibleSize().width; // 背景宽度适应屏幕
    bgSprite.node.height = cc.view.getVisibleSize().height; // 背景高度适应屏幕
    // this.adjustCanvasSize();
    this.rankBG.scale = 1.2;
    this.backBtn.x = -600;
    this.rankBarComponent.opacity = 100;
    this.rankBarComponent.scale = 0.6;
    $_setShareInfo(globalVariables.shareInfo);
  }

  rankBGInAction() {
    return cc.tween().to(this.duration, { scale: 1 });
  }

  backBtnInAction() {
    return cc.tween().to(this.duration, { opacity: 255, x: -430 });
  }

  rankBarComponentInAction() {
    return cc.tween().to(this.duration, { scale: 1, opacity: 255 });
  }

  backBtnTouch() {
    cc.director.loadScene(AppConstants.MAIN_SCENE);
  }

  start() {
    cc.tween(this.rankBG).then(this.rankBGInAction()).start();
    cc.tween(this.backBtn).then(this.backBtnInAction()).start();
    cc.tween(this.rankBarComponent)
      .then(this.rankBarComponentInAction())
      .start();
  }

  adjustCanvasSize() {
    const designResolution = cc.view.getDesignResolutionSize();
    const screenSize = cc.view.getVisibleSize();

    // 计算设计分辨率宽高比和屏幕宽高比
    const designAspect = designResolution.width / designResolution.height;
    const screenAspect = screenSize.width / screenSize.height;

    if (screenAspect > designAspect) {
      // 屏幕更宽：按高度适配（避免左右黑边）
      cc.view.setDesignResolutionSize(
        designResolution.width,
        designResolution.height,
        cc.ResolutionPolicy.FIXED_WIDTH
      );
    } else {
      // 屏幕更窄：按宽度适配（避免上下黑边）
      cc.view.setDesignResolutionSize(
        designResolution.width,
        designResolution.height,
        cc.ResolutionPolicy.FIXED_HEIGHT
      );
    }
    cc.log(`设计分辨率: ${cc.view.getDesignResolutionSize()}`);
    cc.log(`逻辑像素尺寸: ${cc.view.getVisibleSize()}`);
    cc.log(`物理像素尺寸: ${cc.view.getFrameSize()}`);
  }
}
