// 头像
// 20220228
// https://gitee.com/ifaswind/eazax-ccc/blob/master/resources/effects/eazax-avatar.effect

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        texture: { value: white }
        round: { value: 0.1, editor: { tooltip: '圆角半径（百分比）' } }
        feather: { value: 0.01, editor: { tooltip: '边缘虚化（百分比）' } }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>

  in vec3 a_position;
  in vec4 a_color;
  out vec4 v_color;

  #if USE_TEXTURE
  in vec2 a_uv0;
  out vec2 v_uv0;
  #endif

  void main () {
    vec4 pos = vec4(a_position, 1);

    #if CC_USE_MODEL
    pos = cc_matViewProj * cc_matWorld * pos;
    #else
    pos = cc_matViewProj * pos;
    #endif

    #if USE_TEXTURE
    v_uv0 = a_uv0;
    #endif

    v_color = a_color;

    gl_Position = pos;
  }
}%


CCProgram fs %{
  precision highp float;

  #include <texture>

  in vec4 v_color;

  #if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D texture;
  #endif

  // 自定义属性
  uniform Properties {
    float round;    // 圆角半径
    float feather;  // 边缘虚化
  };

  // 获取圆角
  float getAlpha(vec2 uv) {
    vec2 vertex;
    if (uv.x <= round) {
      if (uv.y <= round) {
        vertex = vec2(round, round);                // 左上
      } else if (uv.y >= 1.0 - round) {
        vertex = vec2(round, (1.0 - round));        // 左下
      } else {
        vertex = vec2(round, uv.y);                 // 左
      }
    } else if (uv.x >= 1.0 - round) {
      if (uv.y <= round){
        vertex = vec2(1.0 - round, round);          // 右上
      } else if (uv.y >= 1.0 - round) {
        vertex = vec2(1.0 - round, (1.0 - round));  // 右下
      } else {
        vertex = vec2(1.0 - round, uv.y);           // 右
      }
    } else if (uv.y <= round) {
      vertex = vec2(uv.x, round);                   // 上
    } else if (uv.y >= 1.0 - round) {
      vertex = vec2(uv.x, (1.0 - round));           // 下
    } else {
      vertex = uv;                                  // 中
    }
    // 插值
    float dis = distance(uv, vertex);
    return smoothstep(round, round - feather, dis);
  }

  void main () {
    vec4 color = v_color;

    // 纹理颜色
    #if USE_TEXTURE
    CCTexture(texture, v_uv0, color);
    #endif

    // 跳过透明像素
    if (color.a == 0.0) {
      discard;
    }

    // 处理圆角
    if (round > 0.0) {
      color.a *= getAlpha(v_uv0);
    }

    gl_FragColor = color;
  }
}%
