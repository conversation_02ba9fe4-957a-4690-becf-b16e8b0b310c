// Eazax-CCC 彩色画笔 1.0.0-20201202
// https://gitee.com/ifaswind/eazax-ccc/blob/master/resources/effects/eazax-color-brush.effect

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
          blendSrc: one
          blendDst: one_minus_src_alpha
          blendSrcAlpha: one
          blendDstAlpha: one_minus_src_alpha
      rasterizerState:
        cullMode: none
      properties:
        alphaThreshold: { value: 0.5 }
        size: { value: [500.0, 500.0], editor: { tooltip: '节点尺寸' } }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>

  in vec3 a_position;
  out vec3 v_position;
  
  in vec4 a_color;
  varying vec4 v_color;

  in float a_dist;
  out float v_dist;

  void main () {
    vec4 pos = vec4(a_position, 1);
    pos = cc_matViewProj * cc_matWorld * pos;

    v_position = a_position;
    v_color = a_color;
    v_dist = a_dist;

    gl_Position = pos;
  }
}%


CCProgram fs %{
  #if CC_SUPPORT_standard_derivatives
    #extension GL_OES_standard_derivatives : enable
  #endif

  precision highp float;
  
  #include <alpha-test>

  in vec3 v_position;
  in vec4 v_color;
  in float v_dist;

  uniform Properties {
    vec2 size;
  };

  vec3 gradient(float f) {
    vec3 a = fract(f + vec3(1.0, 2.0 / 3.0, 1.0 / 3.0));
    vec3 b = abs(a * 5.5 - 3.0);
    return clamp(b - 1.0, 0.0, 1.0);
  }

  void main () {
    vec2 pos = v_position.xy / size;
    float d = distance(pos, vec2(1.0, 1.0));
    vec4 o = vec4(gradient(d), 1.0);

    ALPHA_TEST(o);

    #if CC_SUPPORT_standard_derivatives
      float aa = fwidth(v_dist);
    #else
      float aa = 0.05;
    #endif
    
    float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);
    o.rgb *= o.a;
    o *= alpha;

    gl_FragColor = o;
  }
}%
