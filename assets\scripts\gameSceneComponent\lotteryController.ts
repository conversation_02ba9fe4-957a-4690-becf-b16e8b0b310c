// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { ActionUtils } from '../utils/ActionUtils';
import { AppConstants } from '../utils/constants';
import { isInWeixin, pageTo, $_share, getImageURL } from '../utils/generalUtil';
import { globalVariables } from '../utils/GlobalVariables';
// import GlobalDialog from '../globalComponet/GlobalDialog';
const { ccclass, property } = cc._decorator;
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  lotteryFail: cc.Node = null;

  @property(cc.Node)
  lotteryAward: cc.Node = null;

  @property(cc.Node)
  coupon: cc.Node = null;

  @property(cc.Node)
  gold: cc.Node = null;

  @property(cc.Label)
  amountLabel: cc.Label = null;

  @property(cc.Label)
  symbolLabel: cc.Label = null;

  @property(cc.Label)
  goldNumAmount: cc.Label = null;

  @property(cc.Label)
  goldNumText: cc.Label = null;

  @property(cc.Node)
  lotteryBtn: cc.Node = null;

  @property(cc.Node)
  againBtn: cc.Node = null;

  @property(cc.Node)
  shareBtn: cc.Node = null;

  @property(cc.Node)
  acceptBtn: cc.Node = null;

  @property(cc.Node)
  checkBtn: cc.Node = null;

  @property(cc.JsonAsset)
  lotteryInfo: any = null; // 抽奖信息

  duration: number = 0.8;

  // {"code":200,"msg":"成功","data":{"recordId":16,"msg":"5元优惠券","prizeImage":null,"prizeId":2,"prizeType":2},"success":true}
  // {"code":200,"msg":"成功","data":{"recordId":18,"msg":"5金币","prizeImage":null,"prizeId":3,"prizeType":7},"success":true}
  onLoad() {
    this.node.scale = 0.7;
    this.node.opacity = 100;
    if (this.lotteryInfo?.json?.prizeType) {
      this.showAward();
      this.goldNumText.string = this.lotteryInfo.json.msg.toString();
      this.showGold(); // 暂不区分金币和优惠券，统一用金币的样式展示
    } else {
      this.showFail();
    }
  }

  backToLevelSelect() {
    globalVariables.showMarkBlack = false;
    cc.director.loadScene(AppConstants.LEVEL_SELECT_SCENE);
  }

  againBtnClick() {
    this.backToLevelSelect();
  }

  shareBtnClick() {
    // 调用分享接口
    this.node.active = false;
    if (isInWeixin) {
      this.node.parent.getChildByName('shareTip').active = true;
    } else {
      $_share(globalVariables.shareInfo);
      setTimeout(() => {
        this.backToLevelSelect();
      }, 1500);
    }
  }

  acceptBtnClick() {
    this.backToLevelSelect();
  }

  checkBtnClick() {
    this.node.active = false;
    let isCoupon = this.lotteryInfo?.json?.prizeType === 2;
    var url =
      location.origin +
      '/actions/app/mcar?&utm_source=gameH5#/mall/points/list';
    if (isCoupon) {
      url =
        location.origin + '/actions/app/mcar?&utm_source=gameH5#/ticket/list';
    }
    pageTo(url, {
      titleBar: false,
      requireSignIn: true,
      sbarColor: 'black',
      sbarBgColor: '#f2f2f2',
    });
    this.backToLevelSelect();
  }

  showFail() {
    // 显示失败，隐藏中奖
    this.lotteryFail.active = true;
    this.lotteryAward.active = false;
  }

  showAward() {
    // 显示中奖，隐藏失败
    this.lotteryFail.active = false;
    this.lotteryAward.active = true;
  }

  showCoupon() {
    // 显示coupon，隐藏gold
    this.coupon.active = true;
    this.gold.active = false;
  }
  showGold() {
    // 显示gold，隐藏coupon
    this.coupon.active = false;
    this.gold.active = true;
  }
  updateCoupon(amount) {
    // 更新数字内容
    this.amountLabel.string = amount.toString();
    setTimeout(() => {
      // 动态调整￥符号的位置
      const symbolWidth = this.symbolLabel.node.width;
      const amountWidth = this.amountLabel.node.width;
      const currentX = this.amountLabel.node.x; // 获取当前X位置
      this.symbolLabel.node.x =
        currentX - amountWidth / 2 - symbolWidth / 2 - 10;
    }, 0); // 0毫秒的延迟
  }
  updateGold(amount) {
    // 更新数字内容
    this.goldNumAmount.string = amount.toString();
    setTimeout(() => {
      const amountWidth = this.goldNumAmount.node.width;
      const currentX = this.goldNumText.node.x; // 获取当前X位置
      this.goldNumAmount.node.x = currentX - amountWidth / 2 - 10;
    }, 0); // 0毫秒的延迟
  }
  protected onEnable(): void {
    cc.tween(this.node)
      .then(ActionUtils.restoreScaleAndOpacityAction(this.duration))
      .start();

    // btn做呼吸效果
    cc.tween(this.lotteryBtn)
      .then(ActionUtils.largenAndLessenAction(1.1, 0.9, 0.8))
      .repeatForever()
      .start();

    // againBtn和shareBtn交替做呼吸效果
    cc.tween(this.againBtn)
      .then(ActionUtils.largenAndLessenAction(0.9, 0.8, 0.8))
      .repeatForever()
      .start();

    cc.tween(this.shareBtn)
      .delay(0.8)
      .call(() => {
        cc.tween(this.shareBtn)
          .then(ActionUtils.largenAndLessenAction(0.9, 0.8, 0.8))
          .repeatForever()
          .start();
      })
      .start();

    cc.tween(this.acceptBtn)
      .then(ActionUtils.largenAndLessenAction(0.9, 0.8, 0.8))
      .repeatForever()
      .start();

    cc.tween(this.checkBtn)
      .delay(0.8) // 延迟 0.8 秒
      .call(() => {
        cc.tween(this.checkBtn)
          .then(ActionUtils.largenAndLessenAction(0.9, 0.8, 0.8))
          .repeatForever()
          .start();
      })
      .start();
  }

  start() {}

  // update (dt) {}
}
