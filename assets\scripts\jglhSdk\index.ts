/**
 * jglh-js-sdk
 */
import { JglhSDK, Jglh } from './core';
import Utils from './utils';
import Commons from './modules/common';
import Compatible from './modules/compatible';
import Recorder from './modules/recorder';
import Image from './modules/image';
import Screen from './modules/screen';
import WeApp from './modules/weapp';
import JglhParser from './modules/jglh-url-parser';
import Video from './modules/video';
import Location from './modules/location';

JglhSDK.prototype = Object.assign(JglhSDK.prototype, Utils);
JglhSDK.prototype = Object.assign(JglhSDK.prototype, Commons);
JglhSDK.prototype = Object.assign(JglhSDK.prototype, Recorder);
JglhSDK.prototype = Object.assign(JglhSDK.prototype, Image);
JglhSDK.prototype = Object.assign(JglhSDK.prototype, Screen);
JglhSDK.prototype = Object.assign(JglhSDK.prototype, WeApp);
JglhSDK.prototype = Object.assign(JglhSDK.prototype, Compatible);
JglhSDK.prototype = Object.assign(JglhSDK.prototype, Video);
JglhSDK.prototype = Object.assign(JglhSDK.prototype, Location);
JglhSDK.prototype = Object.assign(JglhSDK.prototype, JglhParser);

// JglhSDK.version = '4.5.1';

export default JglhSDK;
// export { Jglh };
