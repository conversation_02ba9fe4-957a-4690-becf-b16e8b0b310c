{"ver": "1.0.27", "uuid": "e373da25-d4f1-4b27-ab36-0616668e57d3", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nattribute vec3 a_position;\nattribute vec4 a_color;\nattribute vec2 a_uv0;\nvarying vec4 v_color;\nvarying vec2 v_uv0;\nvoid main () {\n  gl_Position = cc_matViewProj * vec4(a_position, 1);\n  v_color = a_color;\n  v_uv0 = a_uv0;\n}", "frag": "\nprecision highp float;\nuniform vec4 cc_time;\nvarying vec4 v_color;\nvarying vec2 v_uv0;\nuniform sampler2D texture;\nuniform float amplitude;\nuniform float angularVelocity;\nuniform float frequency;\nuniform float offset;\nuniform bool toLeft;\nvoid main () {\n  vec4 color = v_color;\n  color *= texture2D(texture, v_uv0);\n  if(color.a == 0.0) discard;\n  float y = amplitude * sin((angularVelocity * v_uv0.x) + ((frequency * cc_time.x) * (toLeft ? 1. : -1.))) + offset;\n  if(v_uv0.y < y) discard;\n  gl_FragColor = color;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nin vec3 a_position;\nin vec4 a_color;\nin vec2 a_uv0;\nout vec4 v_color;\nout vec2 v_uv0;\nvoid main () {\n  gl_Position = cc_matViewProj * vec4(a_position, 1);\n  v_color = a_color;\n  v_uv0 = a_uv0;\n}", "frag": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nin vec4 v_color;\nin vec2 v_uv0;\nuniform sampler2D texture;\nuniform Properties {\n  float amplitude;\n  float angularVelocity;\n  float frequency;\n  float offset;\n  bool toLeft;\n};\nvoid main () {\n  vec4 color = v_color;\n  color *= texture(texture, v_uv0);\n  if(color.a == 0.0) discard;\n  float y = amplitude * sin((angularVelocity * v_uv0.x) + ((frequency * cc_time.x) * (toLeft ? 1. : -1.))) + offset;\n  if(v_uv0.y < y) discard;\n  gl_FragColor = color;\n}"}}], "subMetas": {}}