{"name": "enhance-kit", "version": "3.1.0", "description": "This extension provides support for cocos enhance kit.", "author": "SmallMain", "main": "main.js", "scene-script": "scene.js", "panel": {"main": "panel/index.js", "type": "dockable", "title": "i18n:enhance-kit.settings_title", "width": 500, "height": 600, "min-width": 500, "min-height": 600}, "main-menu": {"i18n:MAIN_MENU.project.title/i18n:enhance-kit.settings_menu": {"message": "enhance-kit:openSettings"}, "i18n:MAIN_MENU.package.title/i18n:enhance-kit.COCOS_CREATE_EXTENSION/i18n:enhance-kit.thread_create_custom_thread_menu": {"message": "enhance-kit:createThreadTemplate"}, "i18n:MAIN_MENU.project.title/i18n:enhance-kit.thread_compile_custom_thread_menu": {"message": "enhance-kit:refreshCustomThreadCode"}}, "runtime-resource": {"path": "resources", "name": "resources"}, "reload": {"ignore": ["resources/**/*"]}}