const { ccclass, property, executionOrder } = cc._decorator;

@ccclass
@executionOrder(-100) // 提高执行优先级
export default class ToastManager extends cc.Component {
  @property(cc.Label)
  messageLabel: cc.Label = null;

  @property(cc.Node)
  bgNode: cc.Node = null;

  // 单例实例
  public static instance: ToastManager = null;

  // 队列系统（支持多个Toast排队显示）
  private messageQueue: string[] = [];
  private isShowing: boolean = false;

  onLoad() {
    // 单例初始化
    if (!ToastManager.instance) {
      ToastManager.instance = this;
      cc.game.addPersistRootNode(this.node); // 设为常驻节点
      this.node.active = false; // 默认不显示

      // this.onSceneLoaded;
      // 监听场景切换事件
      cc.director.on(
        cc.Director.EVENT_AFTER_SCENE_LAUNCH,
        this.onSceneLoaded,
        this
      );
      this.adjustBgSize();
    } else {
      this.node.destroy(); // 如果已有实例则销毁当前实例
    }
    // cc.log('Toast子节点绑定状态:', this.bgNode, this.messageLabel); // 调试日志
  }

  // 动态调整背景尺寸
  private adjustBgSize() {
    this.scheduleOnce(() => {
      const labelWidth = this.messageLabel.node.width;
      const labelHeight = this.messageLabel.node.height;
      this.bgNode.height = labelHeight + 40; // 上下各留20像素边距
      this.bgNode.width = labelWidth + 40; // 上下各留20像素边距
    }, 0); // 延迟一帧执行
  }
  // 场景加载完成后，将 Toast 挂载到当前 Canvas 下
  private onSceneLoaded() {
    this.scheduleOnce(() => {
      const canvas = cc.Canvas.instance?.node;
      if (!canvas) {
        cc.log('Canvas 未找到');
        return;
      }

      if (this.node.parent !== canvas) {
        // canvas.addChild(this.node);
        this.updatePosition();
      }
    }, 0); // 延迟一帧执行
  }
  private updatePosition() {
    const canvas = cc.Canvas.instance.node;
    if (!canvas) return;

    const designResolution = cc.view.getDesignResolutionSize();
    const visibleSize = cc.view.getVisibleSize();

    // 根据 Canvas 的适配策略计算实际渲染区域
    let actualWidth = visibleSize.width;
    let actualHeight = visibleSize.height;

    const canvasComponent = canvas.getComponent(cc.Canvas);
    if (canvasComponent.fitHeight) {
      actualWidth =
        designResolution.width * (visibleSize.height / designResolution.height);
    } else if (canvasComponent.fitWidth) {
      actualHeight =
        designResolution.height * (visibleSize.width / designResolution.width);
    }

    // 设置 Toast 到实际渲染区域的中心
    this.node.setPosition(cc.v2(actualWidth / 2, actualHeight / 2));
  }
  /**
   * 显示Toast
   * @param message 提示内容
   * @param duration 显示时长（秒）
   */
  public show(message: string, duration: number = 2) {
    if (!message || message.trim() === '') return; // 过滤空消息
    if (!this.messageQueue) {
      this.messageQueue = [];
    }
    this.messageQueue.push(message);
    this.processQueue(duration); // 处理消息队列
  }

  // 处理消息队列
  private processQueue(duration: number) {
    // debugger;
    // 如果正在显示 Toast 或队列为空，则不再处理
    if (this.isShowing || this.messageQueue.length === 0) return;

    // 从队列中获取并显示消息
    this.isShowing = true;
    const msg = this.messageQueue.shift();
    this.messageLabel.string = msg;

    // 动态调整背景高度（文字换行适配）
    this.scheduleOnce(() => {
      const labelWidth = this.messageLabel.node.width;
      const labelHeight = this.messageLabel.node.height;
      this.bgNode.height = labelHeight + 40; // 上下各留20像素边距
      this.bgNode.width = labelWidth + 40; // 上下各留20像素边距
      // 将背景设置为圆角矩形
      const graphics = this.bgNode.getComponent(cc.Graphics);
      graphics.clear();
      graphics.fillColor = new cc.Color(0, 0, 0, 200);
      graphics.roundRect(
        -this.bgNode.width / 2,
        -this.bgNode.height / 2,
        this.bgNode.width,
        this.bgNode.height,
        20
      );
      graphics.fill();
    }, 0); // 延迟一帧执行

    // 显示动画
    this.node.active = true;
    this.node.opacity = 0;
    this.node.y = 100; // 从底部100像素位置升起

    cc.tween(this.node)
      .to(0.2, { opacity: 255 })
      .delay(duration)
      .to(0.2, { opacity: 0 })
      .call(() => {
        this.node.active = false;
        this.isShowing = false;
        this.processQueue(duration); // 继续处理队列中的下一个
      })
      .start();
  }
  onDestroy() {
    cc.director.off(
      cc.Director.EVENT_AFTER_SCENE_LAUNCH,
      this.onSceneLoaded,
      this
    );
  }
}
