// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;
import { ActionUtils } from './utils/ActionUtils';
import { AppConstants } from './utils/constants';
import { globalVariables } from './utils/GlobalVariables';
import Request from './apis/api';
import ToastManager from './globalComponet/ToastManager';
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  BG: cc.Node = null;

  @property(cc.Node)
  backBtn: cc.Node = null;

  @property(cc.Node)
  title: cc.Node = null;

  @property(cc.Node)
  levelSelectionComponent: cc.Node = null;

  @property(cc.Node)
  markBlack: cc.Node = null;

  duration: number = 0.8;
  levelSelectionScale: number = 1;

  backToMainScene() {
    cc.director.loadScene(AppConstants.MAIN_SCENE);
  }

  protected onLoad(): void {
    // // 设置为常驻节点，确保在场景切换时不销毁
    // cc.game.addPersistRootNode(this.ToastNode);
    this.BG.scale = 1.2;
    this.title.scale = 0.5;
    this.title.opacity = 100;
    this.levelSelectionComponent.scale = 0.5;
    this.levelSelectionComponent.opacity = 100;
    this.backBtn.opacity = 0;
    this.backBtn.y = 454;

    // 获取背景图，确保适应屏幕
    const { width, height } = cc.view.getVisibleSize();
    const bgSprite = this.BG.getComponent(cc.Sprite);
    bgSprite.node.width = width; // 背景宽度适应屏幕
    bgSprite.node.height = height; // 背景高度适应屏幕
    cc.director.preloadScene(AppConstants.GAME_SCENE, function () {
      cc.log('Next scene preloaded');
    });
    // 预加载可玩关卡背景资源
    let totalCount = globalVariables.passLevelArray.filter(
      item => item == 1
    ).length;
    for (let i = 1; i <= totalCount; i++) {
      for (let j = 1; j <= 3; j++) {
        cc.resources.preload(
          `coreGameSceneMaterial/image/level${i}/bg/${j}`,
          cc.SpriteFrame
        );
      }
    }

    if (globalVariables.showMarkBlack) {
      this.markBlack.active = true;
    }
    // 处理折叠屏，屏幕较长时，levelSelectionComponent较大问题，缩小，并左移
    let designRate = 0.56; // 设计尺寸 640/1136 = 0.56
    let screenRate = width / height;
    let scale = screenRate / designRate;
    this.levelSelectionScale = scale;
    // cc.log(`设计分辨率: ${cc.view.getDesignResolutionSize()}`);
    // cc.log(`逻辑像素尺寸: ${cc.view.getVisibleSize()}`);
    // cc.log(`物理像素尺寸: ${cc.view.getFrameSize()}`);
  }

  start() {
    // 1. 动画特效
    cc.tween(this.BG)
      .then(ActionUtils.restoreSizeAction(this.duration))
      .start();
    cc.tween(this.levelSelectionComponent)
      .then(
        cc
          .tween()
          .to(this.duration, { scale: this.levelSelectionScale, opacity: 255 })
      )
      .start();
    cc.tween(this.title)
      .then(ActionUtils.restoreScaleAndOpacityAction(this.duration))
      .start();
    cc.tween(this.title)
      .then(
        cc
          .tween()
          .repeatForever(
            ActionUtils.largenAndLessenAction(1, 0.9, this.duration)
          )
      )
      .start();
    cc.tween(this.backBtn)
      .then(cc.tween().to(this.duration, { y: 354, opacity: 255 }))
      .start();
  }

  // update (dt) {}
}
