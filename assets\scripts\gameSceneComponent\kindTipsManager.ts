// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import { ActionUtils } from '../utils/ActionUtils';
import { globalVariables } from '../utils/GlobalVariables';

@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  maskSuccess: cc.Node = null;

  duration: number = 0.3;
  componentScale: number = 1.3;

  nodeScale: number = 1;

  // LIFE-CYCLE CALLBACKS:

  knowBtn() {
    this.node.active = false;
    this.maskSuccess.active = true;
  }

  onLoad() {
    const { width, height } = cc.view.getVisibleSize();
    // 处理折叠屏，屏幕较长时，levelSelectionComponent较大问题，缩小，并左移
    let designRate = 0.56; // 设计尺寸 640/1440 = 0.44
    let screenRate = width / height;
    let scale = screenRate / designRate;
    this.nodeScale = scale;
    cc.log(`设计分辨率: ${cc.view.getDesignResolutionSize()}`);
    cc.log(`逻辑像素尺寸: ${cc.view.getVisibleSize()}`);
    cc.log(`物理像素尺寸: ${cc.view.getFrameSize()}`);
  }

  protected onEnable(): void {
    // this.scrollView.scrollToTop();
    this.node.scale = this.nodeScale * 0.7;
    this.node.opacity = 100;
    cc.tween(this.node)
      .then(
        cc.tween().to(this.duration, { scale: this.nodeScale, opacity: 255 })
      )
      .start();
  }

  start() {}

  // update (dt) {}
}
