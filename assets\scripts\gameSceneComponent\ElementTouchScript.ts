// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import { globalVariables } from '../utils/GlobalVariables';
import Request from '../apis/api';
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  maskMessage: cc.Node = null;

  currentLevelKnowledgeData =
    globalVariables.LevelKnowledgeData[globalVariables.currentLevel - 1];

  onLoad() {
    this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
  }

  onTouchEnd(event) {
    let maskMessage = cc.find('Canvas/maskMessage');

    console.log('been touch1');
    // 获取当前节点在父节点中的index
    let index = this.node['elementIndex']; // 获取同级索引
    if (globalVariables.currentFoundElementIndexArray[index] !== 1) {
      globalVariables.currentFoundElement++;
    }
    globalVariables.currentFoundElementIndexArray[index] = 1;
    // 显示当前隐患提示
    let explainBg = this.node.getChildByName('explainBg');

    if (explainBg && !explainBg.active) {
      explainBg.active = true;
      // // 先将explainBg的透明度设置为0
      // explainBg.opacity = 0;
      let textLabel = explainBg.getChildByName('explainText');
      let position = this.currentLevelKnowledgeData[index].tipsPosition;
      explainBg.width = textLabel.width + 40;
      explainBg.height = textLabel.height + 60;
      explainBg.x = position ? position[0] : 0;
      explainBg.y = position ? position[1] : 0;
      cc.tween(explainBg).by(0.5, { x: 110, opacity: 255 }).start();
      // }, 500);
    }

    maskMessage.getComponent('maskMessageManager').knowledgeIndex = index;
    setTimeout(() => {
      // 设置maskMessage的index属性
      maskMessage.active = true;
    }, 500);
    // 移除当前节点的触摸结束事件监听器
    // this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
  }
  onDestroy() {
    cc.systemEvent.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
  }
}
