export default {
  /**
   * 设置屏幕亮度
   * @param {object} option
   * @param {number} option.value - 亮度值：0-1之间
   * @param {function} option.success - 调用成功
   * @param {function} option.fail - 调用失败
   * @param {function} option.complete: - 调用结束
   */
  setScreenBrightness: function (option) {
    var methodName = 'setScreenBrightness';
    // var settings = this.fnFilter(option, false);
    this.callNative(methodName, option);
  },
  /**
   * 获取屏幕亮度
   * @param {object} option
   * @param {function} option.success(value: number) - 调用成功传入屏幕亮度
   * @param {function} option.fail - 调用失败
   * @param {function} option.complete: - 调用结束
   */
  getScreenBrightness: function (option) {
    var methodName = 'getScreenBrightness';
    // var settings = this.fnFilter(option, false);
    this.callNative(methodName, option);
  },
  /**
   * 设置屏幕亮度
   * @param {object} option
   * @param {boolean} option.keepScreenOn - 是否常亮
   * @param {function} option.success - 调用成功
   * @param {function} option.fail - 调用失败
   * @param {function} option.complete: - 调用结束
   */
  setKeepScreenOn: function (option) {
    var methodName = 'setKeepScreenOn';
    // var settings = this.fnFilter(option, false);
    this.callNative(methodName, option);
  },
  /**
   * 设置截屏事件回调函数
   * @param {function} callback - 截屏回调函数
   */
  onUserCaptureScreen: function (callback) {
    var methodName = 'onUserCaptureScreen';
    // var settings = this.createCallbackForNative(callback, methodName);
    // var settings = this.fnFilter({
    //   complete: callback,
    // }, methodName);
    var options = {
      complete: callback,
    };
    this.callNative(methodName, options);
  },
}
