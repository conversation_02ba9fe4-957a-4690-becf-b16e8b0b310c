// 与小程序若干交互的方法
export default {
  /**
   * 跳转到微信小程序
   * @param {object} option
   * @param {string} userName: '', // 要打开的小程序 原始id
   * @param {string} path: 跳转路径如'page/index/index?id=123',
   * @param {string} envVersion: 'develop', // develop|trial|release
   * @param {string} success 成功回调
   * @param {function} fail 失败回调
   */
  navigateToMiniProgram: function (option) {
    var methodName = 'navigateToMiniProgram';
    // var settings = this.fnFilter(option, false);
    this.callNative(methodName, option);
  },
  /**
   * 获取小程序回调数据
   * @param {object} option
   * @param {function} option.success - 调用成功
   * @param {function} option.fail - 调用失败
   */
  getMiniProgramMessage: function (option) {
    var methodName = 'getMiniProgramMessage';
    this.callNative(methodName, option);
  },
  /**
   * 设置小程序回调
   * @param {function} callback - 回调函数
   */
  onMiniProgramMessage: function (callback) {
    var methodName = 'onMiniProgramMessage';
    var options = {
      complete: callback,
    };
    this.callNative(methodName, options);
  },

    /**
  * 跳转到滴滴加油或者滴滴充电
  * @param {object}   option
  * @param {string}   option.type - 打开类型 "oil"：加油 "charge" 充电
  * @param {string}   option.url - 加载地址，（可以为空，则原生请求加载地址）
  */
  openDidi: function (option) {
    this.callNative('openDidi', option);
  },

  /**
  * 直接调用分享功能
  * @param {object}   option
  * @param {string}   option.title - 分享标题
  * @param {string}   option.link - 分享链接  8 网页分享，link 网页链接地址; 6 图片分享，link 视频链接地址;
  * @param {string}   option.imgUrl - 分享图标
  * @param {function} option.success - 分享成功回调
  * @param {function} option.cancel - 取消分享回调
  
  // 456版本直接分享新增参数
  * @param {int}   option.type - 分享类型
  * @param {byte}   option.image - 分享链接
  * @param {string}   option.platform - 分享渠道
  * @param {string}   option.shareMinPath - 小程序页面路径
  * @param {string}   option.shareMinId - 小程序原始id,在微信平台查询 gh开头
  *    type:
              SHARE_TYPE_IMAGE = 6;//图片分享
              SHARE_TYPE_VIDEO = 7;//视频分享
              SHARE_TYPE_WEB = 8;//网页分享
              SHARE_TYPE_MIN = 9;//小程序分享

      platform:
              SHARE_PLATFORM_WEIXIN = "WEIXIN";//分享微信平台
              SHARE_PLATFORM_WEIXIN_CIRCLE = "WEIXIN_CIRCLE";//分享微信朋友圈平台
              SHARE_PLATFORM_SINA = "SINA";//分享新浪平台
              
      image：
              图片分享支持传入图片的链接地址imgUrl 或者 图片的byte数据image 作为分享数据
      
      link: 
              8 网页分享，link 网页链接地址
              6 图片分享，link 视频链接地址
  */
  shareDirect: function (option) {
    this.callNative('shareDirect', option);
  },
}
