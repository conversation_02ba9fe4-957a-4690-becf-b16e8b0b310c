// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;
import { AppConstants } from '../utils/constants';
import { globalVariables } from '../utils/GlobalVariables';

@ccclass
export default class NewClass extends cc.Component {
  onLoad() {
    this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
  }

  onTouchEnd(event) {
    this.node.parent.active = false;
    this.backToLevelSelect();
    // // 移除当前节点的触摸结束事件监听器
    this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
  }
  backToLevelSelect() {
    globalVariables.showMarkBlack = false;
    cc.director.loadScene(AppConstants.LEVEL_SELECT_SCENE);
  }

  onDestroy() {
    cc.systemEvent.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
  }
}
