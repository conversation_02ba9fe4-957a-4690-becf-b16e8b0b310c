export default {
  detectType: function (type, o) {
    return new RegExp(type).test(Object.prototype.toString.apply(o));
  },

  isPlainObject: function (o) {
    return (
      o && typeof o == 'object' && Object.getPrototypeOf(o) === Object.prototype
    );
  },

  isString: function (o) {
    return this.detectType('String', o);
  },

  isArray: function (o) {
    return this.detectType('Array', o);
  },

  isFunction: function (o) {
    return this.detectType('Function', o);
  },

  isNumber: function (o) {
    return /^(-)?\d+(\.\d+)?$/.test(o);
  },

  // 加载js，src可以是字符串或字符串数组
  loadScript: function (src) {
    var that = this;

    if (src instanceof Array) {
      for (var i in src) that.loadScript(src[i]);
      return;
    }

    var script = document.createElement('script');
    script.src = src;
    document.getElementsByTagName('head')[0].appendChild(script);
    return that;
  },

  // 加载css，src可以是字符串或字符串数组
  loadCSS: function (src) {
    var that = this;

    if (src instanceof Array) {
      for (var i in src) that.loadCSS(src[i]);
      return;
    }

    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = src;
    document.getElementsByTagName('head')[0].appendChild(link);
    return that;
  },

  // 获取一个相对路径的绝对URL
  getAbsolutePath: function (src) {
    var link = document.createElement('link');
    link.href = src;
    return link.href;
  },
  getVersion: function () {
    const result = /JGRM1041_(\d+)/i.exec(navigator.userAgent);
    if (result instanceof Array && result.length === 2) {
      return Number(result[1]);
    }
    // return 0;
    return 380;
  },
  /**
   * 获取交广领航app版本号
   * @return {string} version 版本号
   */
  getJglhVersion: function () {
    const result = /jglh\/(\w+)/i.exec(navigator.userAgent);
    if (result instanceof Array && result.length === 2) {
      return result[1];
    }
    return '';
  },
  extend: function (target, props) {
    var that = this;

    if (props instanceof Array) {
      props.forEach(function (item) {
        that.extend(target, item);
      });
      return target;
    }

    for (var prop in props) {
      if (props.hasOwnProperty(prop)) {
        // console.log(prop, '=' ,props[prop]);
        target[prop] = props[prop];
      }
    }
    return target;
  },
};
