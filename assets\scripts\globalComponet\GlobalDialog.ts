// GlobalDialog.ts
const { ccclass, property } = cc._decorator;

@ccclass
export default class GlobalDialog extends cc.Component {
  @property(cc.Label)
  titleLabel: cc.Label = null;

  @property(cc.Label)
  contentLabel: cc.Label = null;

  @property(cc.Button)
  closeButton: cc.Button = null;

  private static _instance: GlobalDialog = null;

  public static get instance(): GlobalDialog {
    return this._instance;
  }

  onLoad() {
    // 设置单例
    if (GlobalDialog._instance === null) {
      GlobalDialog._instance = this;
    }
  }

  onDestroy() {
    // 清理单例
    if (GlobalDialog._instance === this) {
      GlobalDialog._instance = null;
    }
  }

  // 显示弹窗（可自定义内容）
  show(title: string, content: string) {
    this.titleLabel.string = title;
    this.contentLabel.string = content;
    this.node.active = true;
    this.playShowAnimation();
  }

  // 隐藏弹窗
  hide() {
    this.node.active = false;
  }

  onCloseClick() {
    this.hide();
  }
  // 弹窗动画（示例）
  private playShowAnimation() {
    this.node.scale = 0.5;
    cc.tween(this.node).to(0.3, { scale: 1 }, { easing: 'backOut' }).start();
  }
}
