import { generateUUID } from './generalUtil';

const storage = localStorage;

export function createKey(name) {
  const PREFIX = 'jglh:mcar:';
  return `${PREFIX}${name}`;
}

export function removeItem(key) {
  storage.removeItem(key);
}

export function setItem(key, value) {
  storage.setItem(key, value);
}

export function getItem(key) {
  return storage.getItem(key);
}

export default {
  removeItem,
  setItem,
  getItem,
};

const Keys = {
  AUTH_TOKEN: 'session', // 权限认证token，因此key涉及多个模块，暂时不加前缀
};

/**
 * 保存登录验证token，登录token值由于跨项目原因，采取特殊处理，使用的key值统一为session
 * @param {*} value
 */
export function saveTokenToStorage(value) {
  storage.setItem(Keys.AUTH_TOKEN, value);
}

/**
 * 获取登录验证token
 */
export function getTokenFromStorage() {
  // 2019年1月18日15:18:55读写session key 值变更，为不影响线上微信端已登录的用户，此处做回退降级处理
  return storage.getItem(Keys.AUTH_TOKEN);
}

/**
 * 生成一个uuid，并存储到localStorage中
 * 主要用于唯一标记客户端，供服务端做数据分析用
 */
export function getFingerprint() {
  let KEY = 'cid';
  try {
    let id = getItem(KEY);
    if (id) {
      return id;
    } else {
      id = generateUUID();
      setItem(KEY, id);
      return id;
    }
  } catch (err) {
    return '';
  }
}

export function saveFingerprint(value) {
  let KEY = 'cid';
  setItem(KEY, value);
}
