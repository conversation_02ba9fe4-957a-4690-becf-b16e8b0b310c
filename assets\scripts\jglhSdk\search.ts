import Utils from './utils';

export default class Search {
	constructor() {
		this.init();
	}
	// 初始化参数
	init() {
		this.params = this.query();
	}
	
	// 重置参数
	reset() {
		this.params=this.query();
	}
	
	getParams() {
		return this.params;
	}
	
    /**
     * 将字符串解析为对象
     * @alias module:search.deParam
     * @param {Object} str - 要解析的序列化字符串
     * @return {Object} - 反序列化的对象
     */
	deParam(str) {
	    str=str.replace(/\?/,'');
	    if(!str) return {};
	    var list = str.split('&').map(function(item){
             var param = item.split('=');
             var res={}, name=param[0];
             var value=decodeURIComponent(item.replace(new RegExp('^'+name+'=?'), ''));
			 var result={};
			 result[name] = value;
			 return result;
        })
	    return Utils.extend({}, list)
	}
	
	/**
	 * 将对象序列化为字符串
     * @alias module:search.serialize
	 * @param {Object} obj - 要序列化的对象
     * @return {String} - 序列化的对象
	 */
	serialize(obj) {
		var arr=[];
		for(var key in obj){
			var value=obj[key];
			if(Utils.isString(value) || Utils.isNumber(value)) arr.push(key+'='+value);
			if(Utils.isArray(value)){
				value.forEach(function(v){
					
					// 数组参数名加中括号，兼容php
					arr.push(key+'[]='+v);
				})
			}
		}
		return arr.join('&')
	}
	
	/**
	 * 将当前页面url参数解析为对象
     * @alias module:search.query
	 * @return {Object} 当前页面URL参数对象
	 */
	query() {
		return this.deParam(location.search)
	}
	
	/**
	 * 设置参数
     * @alias module:search.setQuery
	 * @param {Object|String} arg1 - 要添加的参数对象或参数name
	 * @param {String} arg2 - 当arg1为对象时，arg2可为空，arg1为string类型时，arg2为要设置的参数值
	 */
	setQuery(arg1 , arg2) {
		var that=this;
		if(Utils.isPlainObject(arg1)){
			for(var key in arg1) that.setQuery(key, arg1[key]);
			return this.params;
		}
		this.params[arg1]=arg2;
		return this.params;
	}
	
	/**
     * 移除参数
     * @alias module:search.removeQuery
     * @param {String|Array} value 从参数对象中移除某个或某组参数
	 */
	removeQuery(value) {
		var that=this;
		if(Utils.isArray(value)){
			for(var i in value) that.removeQuery(value[i]);
			return this.params;
		}
		delete this.params[value];
		return this.params;
	}
	
	/**
	 * 将参数转化为完整的URI
     * @alias module:search.toString
     * @return {String} 返回当前页面完整的URI
	 */
	toString() {
		var params=this.params;
		
		var searchStr="?";
		for(var key in params){
			searchStr+=[key,"=",params[key],'&'].join("");
		}
		searchStr=searchStr.replace(/\&$/,"")
		return [location.protocol,'//',location.hostname,location.pathname,searchStr,location.hash].join("")
	}
}
