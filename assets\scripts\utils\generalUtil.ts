import {
  parseJglhURL,
  pushWebView,
  login,
  share,
  initAppConfig,
} from './bridge';
import { handleJglhAndWeixinWebViewConfigParam } from './weixin';
import JglhWeApp from './JglhWeApp';
import Request from '../apis/api';
import { globalVariables } from './GlobalVariables';

/**
 * 系统常量
 */
export const isInJglh =
  /jglh/i.test(navigator.userAgent) || /jgrm/i.test(navigator.userAgent);
export const isInJGLH = isInJglh;
export const isDevtools = /devtools/i.test(navigator.userAgent);
export const isAndroid = /android/i.test(navigator.userAgent);
export const isInWeixin = /MicroMessenger/i.test(navigator.userAgent); // 是否是微信环境
export const isInUnionPayMP = /com.unionpay/i.test(navigator.userAgent); // 是否是云闪付环境
export const isIOS = /iphone|ipad|ipod/i.test(navigator.userAgent);
export const isIPhoneX = /iphonex/i.test(navigator.userAgent); // iPhoneX手机的交广领航webview UA信息包含iphonex字符串
export const isProduction = /radio.jgrm.net/i.test(location.host);
export const isInAliApp = navigator.userAgent.indexOf('AliApp') > -1; // 是否是支付宝小程序
export const isInWeApp =
  (window.__wxjs_environment === 'miniprogram' ||
    /miniProgram/.test(navigator.userAgent)) &&
  !isInAliApp; // 是否是微信小程序,支付宝小程序中也有miniprogram
export const isInPC = window.screen.width >= 1080; // 是否是PC环境
export const isInWeixinH5 = isInWeixin && !isInWeApp; // 是否是微信网页环境

export function generateUUID(): string {
  let d = new Date().getTime();
  let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
    /[xy]/g,
    function (c) {
      let r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c == 'x' ? r : (r & 0x7) | 0x8).toString(16);
    }
  );
  return uuid;
}
export function getDataType(value) {
  return Object.prototype.toString.apply(value).slice(8, -1);
}

export function isPlainObject(value) {
  return getDataType(value) == 'Object';
}

export function isArray(value) {
  return getDataType(value) == 'Array';
}

export function isFunction(value) {
  return getDataType(value) == 'Function';
}

export function isRegExp(value) {
  return getDataType(value) == 'RegExp';
}

export function isNumber(o) {
  return /^\d+(\.\d+)?$/.test(o);
}

export function isValidURL(o) {
  return /^http(s)?:\/\/.+$/.test(o);
}

export const getType = getDataType;

/**
 * 判断一个变量是否只指定的类型字符串
 * type(123).is('Number'): true
 * type(123).is('String'): false
 */
export function type(value) {
  return {
    is: type => getDataType(value) === type,
  };
}
/**
 * 获取SPA应用完整url
 * 当前页面会通过判断url中是否包含nohead来动态设置是否采用模拟的标题栏
 * 调用此方法时，默认会将search参数重置
 * @param {string} path spa内路径
 * @param {object} options 配置参数
 */
export function getAppURL(
  path,
  options = {
    search: '?jglh',
  }
) {
  if (isValidURL(path)) return path;
  const location = window.location;
  // const search = options.search ? options.search : location.search;
  const urlParams = new URLSearchParams(location.search);
  const scene = urlParams.get('scene'); // 用于区分营销渠道
  let search = '';
  if (options.search) {
    search = scene ? `${options.search}&scene=${scene}` : options.search;
  } else {
    search = location.search;
  }
  // 目前页面依据location.search或location.hash中是否包含`nohead`来决定是否显示标题栏
  // 当第一次打开webview，使用原生标题栏，构造url如：http://test.com?nohead&a=1#/abc时，
  // 若从该页面再调用pushWebView方法，使用html的标题栏时，就会导致打开的新页面没有标题栏，
  // 为了避免此问题，需要将nohead参数做无效化处理（有一定几率误伤
  // const search2 = search.replace(/nohead/g, 'n_h');
  let path2 = path;
  /* eslint-disable */
  // 仅处理url中的中文字符，进行encodeURIComponent转义，解决iOS端打开带有白屏问题
  const toBeEncodes = /[^\x00-\xff]/g;
  // const toBeEncodes = /[\u4e00-\u9fa5]/g
  if (toBeEncodes.test(path2)) {
    path2 = path2.replace(toBeEncodes, w => {
      return encodeURIComponent(w);
    });
  }
  const result = `${location.origin}${location.pathname}${search}#${path2}`;
  console.log(result);
  return result;
}

/**
 * getAbsolutePath方法名容易产生歧义
 */
export function getAbsolutePath(...args: Parameters<typeof getAppURL>) {
  console.error('getAbsolutePath 方法即将废弃，请使用 getAppURL ');
  return getAppURL(...args);
}

export function pageTo(path: string, options: any, search = '?jglh') {
  let loggedIn = globalVariables.loggedIn;
  if (options.requireSignIn && !loggedIn) {
    authLogin(path);
    return;
  }
  // pushWebView方法依赖于交广领航JS-SDK，只能在交广领航环境下使用
  const fullURL = getAppURL(path, {
    search: options.titleBar ? '?jglh&nohead' : search,
  });
  // 解析jglh-url，忽略默认参数
  const parsedResult = parseJglhURL(fullURL, true);
  // lh_wvc参数用于交广领航app识别，其他环境中无用，此处将其移除
  const fullURL2 = handleJglhAndWeixinWebViewConfigParam(parsedResult.url);
  // debugger
  if (isInJglh) {
    const defaultWebViewOptions = {
      title: '',
      renderer: 'x5',
      shareButton: false,
      titleBar: false,
      progressBar: false,
      sbarColor: 'white',
      pulldownRefresh: false,
      autoUpdateTitle: false,
    };
    // 自定义theme字段，，简化传参
    if (options.theme === 'light') {
      options.sbarColor = 'black';
      options.sbarBgColor = '#ffffff';
      // options.sbarBgColor = '#fdfdfd';
      delete options.theme;
    }
    // 参数优先级：lh_wvc参数 > 方法传入参数 > 默认参数
    const opts = Object.assign(defaultWebViewOptions, options, {
      ...parsedResult,
      url: fullURL2,
    });
    pushWebView(opts);
  } else if (isInWeApp) {
    let query = `url=${encodeURIComponent(fullURL2)}`;
    if (options.shareButton === false) {
      query += '&share=0';
    }
    JglhWeApp.openMiniProgramPage({
      url: '/pages/web/index', // 跳转页面地址, 默认首页
      query: query, // 传递参数 a=1&b=2
    });
  } else {
    // 非交广领航环境下只能通过location刷新页面跳转
    if (options.replace) {
      location.replace(fullURL2);
    } else {
      location.href = fullURL2;
    }
  }
}

export function authSyncUserInfo() {
  return Request.getUserInfo().then(res => {
    globalVariables.userInfo = res;
  });
}

export function authLogin(nextPage?: string) {
  let path = nextPage || location.href;
  if (isInWeixin || isInJglh || isInUnionPayMP || isInAliApp) {
    return login(path).then(res => {
      return authCheckSession(true).then(res => {
        // 若用户已登录，同步用户信息，其中包含了用户是否是vip，用户资料等信息
        if (res.ok) {
          return authSyncUserInfo();
        }
      });
    });
  } else {
    return new Promise(() => {
      // dialog('提示').confirm('请在交广领航App或微信App中访问本页面', {
      //   ok() {
      //     let path = 'https://radio.jgrm.net/actions/app/wechat/wx2app.html?id=4'
      //     location.href = path
      //   }
      // });
    });
  }
}

export function authCheckSession(forceCheck) {
  return checkSession(forceCheck).then(res => {
    if (res.ok) {
      authSyncUserInfo();
    }
    return res;
  });
}

/**
 * 检查session状态是否有效
 * @param {*} param0
 * @param {boolean} forceCheck 是否忽略缓存强制校验
 */
export function checkSession(forceCheck) {
  let loggedIn = globalVariables.loggedIn;
  const shouldUseCachedStatus = !forceCheck && loggedIn;
  if (shouldUseCachedStatus) return Promise.resolve({ ok: true });
  return Request.checkSession().then(res => {
    globalVariables.loggedIn = !!res.ok;
    return res;
  });
}

export function $_setShareInfo(options) {
  const mergeData = Object.assign({}, globalVariables.shareInfo, options);
  setTimeout(() => {
    initAppConfig(mergeData);
  }, 100);
}

export function $_share(options) {
  if (isInWeixin) {
    // dialog().alert('请点击微信右上角的按钮分享页面', {
    //   title: '提示',
    // });
  } else {
    share(options);
  }
}
/**
 * 根据资源获取图片url，支持七牛云图片id，或图片url，图片路径
 * 曾经有老版本app（3.8.x）预览图片不支持https协议的图片地址
 * @param {string} value 图片ID或路径
 * @param {string} type 可选，七牛云图片参数
 * @param {string} protocol 图片协议
 */
export function getImageURL(value, type = '?', protocol = 'https:') {
  // console.log(...arguments);
  if (!value) return '';
  // file, data, bolb, http, https ...
  if (/^\w+:/.test(value)) return value;

  // `./` 开头 认为是相对路径
  if (/^\./.test(value)) return value;

  // `/` 开头认为是绝对路径
  if (/^\/\//.test(value)) return value;

  // `img/` 目录开头的，认为是相对路径
  if (/^img\//.test(value)) return value;

  // android某版本有些图片id是路径，如/storage/6633-6466/DCIM/Camera/20161002_154738.jpg
  if (/^\//.test(value) && !/storage/.test(value)) return value;

  // 其他值都认为是
  return `${protocol}//img.jgrm.net/${value}${type}`;
}
