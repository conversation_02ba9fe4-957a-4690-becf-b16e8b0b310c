/**
 * 系统常量
 */
export const isInJglh = /jgrm/i.test(navigator.userAgent);
export const isInJGLH = isInJglh;
export const isAndroid = /android/i.test(navigator.userAgent);
export const isInWeixin = /MicroMessenger/i.test(navigator.userAgent); // 是否是微信网页环境
export const isIOS = /iphone|ipad|ipod/i.test(navigator.userAgent);
export const isIPhoneX = /iphonex/i.test(navigator.userAgent); // iPhoneX手机的交广领航webview UA信息包含iphonex字符串
export const isProduction = /radio.jgrm.net/i.test(location.host);
export const isInWeApp = window.__wxjs_environment === 'miniprogram' || /miniProgram/.test(navigator.userAgent); // 是否是微信小程序环境

export default {
  jglh: isInJGLH,
  android: isAndroid,
  ios: isIOS,
  iphonex: isIPhoneX,
  weixin: isInWeixin,
  production: isProduction,
  weapp: isInWeApp,
}
