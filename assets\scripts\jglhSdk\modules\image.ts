export default {
  /**
   * 选择图片
   */
  chooseImage: function (options) {
    var that = this;
    this.callNative('chooseImage', options)
  },

  /**
   * 预览图片
   * @param {string} imageId  - 预览的图片id
   */
  previewImage: function (images) {
    this.callNative('previewImage', images)
  },

  /**
   * 预览图片
   * @param {string} imageId  - 预览的图片id
   */
  playPhotos: function (option) {
    this.callNative('playPhotos', option)
  },
  /**
   * 保存网络图片到相册
   * @param {string} url  - 图片的URL地址
   */
  saveImage: function (url) {
    this.callNative('saveImage', url)
  }

}
