// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;
import { ActionUtils } from './utils/ActionUtils';
import { AppConstants } from './utils/constants';
import { globalVariables } from './utils/GlobalVariables';
import Request from './apis/api';
import ToastManager from './globalComponet/ToastManager';
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  BG: cc.Node = null;

  @property(cc.Node)
  backBtn: cc.Node = null;

  @property(cc.Node)
  drawTube: cc.Node = null;

  @property(cc.Node)
  markBlack: cc.Node = null;

  duration: number = 0.8;
  levelSelectionScale: number = 1;

  backToMainScene() {
    cc.director.loadScene(AppConstants.MAIN_SCENE);
  }

  protected onLoad(): void {
    // // 设置为常驻节点，确保在场景切换时不销毁
    // cc.game.addPersistRootNode(this.ToastNode);
    this.BG.scale = 1.2;
    this.drawTube.scale = 0.5;
    this.drawTube.opacity = 100;
    this.backBtn.opacity = 0;
    this.backBtn.y = 454;

    // 获取背景图，确保适应屏幕
    const { width, height } = cc.view.getVisibleSize();
    const bgSprite = this.BG.getComponent(cc.Sprite);
    bgSprite.node.width = width; // 背景宽度适应屏幕
    bgSprite.node.height = height; // 背景高度适应屏幕

    // 处理折叠屏，屏幕较长时，levelSelectionComponent较大问题，缩小，并左移
    let designRate = 0.56; // 设计尺寸 640/1136 = 0.56
    let screenRate = width / height;
    let scale = screenRate / designRate;
    this.levelSelectionScale = scale;
    // cc.log(`设计分辨率: ${cc.view.getDesignResolutionSize()}`);
    // cc.log(`逻辑像素尺寸: ${cc.view.getVisibleSize()}`);
    // cc.log(`物理像素尺寸: ${cc.view.getFrameSize()}`);
  }

  start() {
    // 1. 动画特效
    cc.tween(this.BG)
      .then(ActionUtils.restoreSizeAction(this.duration))
      .start();
    cc.tween(this.drawTube)
      .then(
        cc
          .tween()
          .to(this.duration, { scale: this.levelSelectionScale, opacity: 255 })
      )
      .start();
    cc.tween(this.backBtn)
      .then(cc.tween().to(this.duration, { y: 354, opacity: 255 }))
      .start();
  }

  // update (dt) {}
}
