import Utils from './utils';
import configs from './config';
import ENV from './env';
// import {
//   Jglh
// } from '../core';

/**
 * 定义define，use等方法，主要是为了兼容老版本的使用方式
 */
export class Jglh {
  constructor() {
    this.modules = {};
  }
  define(name, module) {
    this.modules[name.toLowerCase()] = module;
    return this;
  }
  use(name) {
    if (name === 'native') return this;
    var Module = this.modules[name.toLowerCase()];
    if (!Module) throw new Error('module ' + name + ' not found!');
    if (Utils.isFunction(Module)) {
      return new Module();
    } else {
      return Module;
    }
  }
}

// 创建供native可调用的回调函数
function createCallbackForNative(fn, fnName) {
  var poolName = '_appjs_callbacks';
  var pool = window[poolName];
  if (!pool) pool = window[poolName] = {};
  const randomName = String(Math.random()).replace(/^0\./g, '');
  // 添加随机数是为了防止当
  // var name = `_callback_${fnName}_${randomName}`;
  var name = `_callback_${fnName}`;
  // if (name in pool) {
  //   createCallbackForNative(fn);
  //   return;
  // }

  pool[name] = function (arg) {
    try {
      const args = [...arguments].map(item => {
        return JSON.parse(item);
      });
      fn.apply(Jglh.prototype, args);
    } catch {
      fn.apply(Jglh.prototype, arguments);
    }
    // removeCallbackForNative(name); // 执行后删除函数
  };
  return `window.${poolName}.${name}`;
  // return 'window.' + poolName + '.' + name;
}

// 移除回调函数
function removeCallbackForNative(name) {
  var poolName = '_appjs_callbacks';
  var pool = window[poolName];
  if (name in pool) {
    pool[name] = null;
    delete pool[name];
  }
}

// 处理参数中的函数
function fnFilter(method, option) {
  for (var value in option) {
    if (Utils.isFunction(option[value])) {
      option[value] = createCallbackForNative(
        option[value],
        `${method}_${value}`
      );
    }
  }
  return option;
}

// 将数据中的函数转换为字符串
function transformData(methodName, data) {
  if (Utils.isString(methodName) && Utils.isPlainObject(data))
    return fnFilter(methodName, data);
  return data;
}

// 数据兼容处理：由于app端数据类型局限，此处需要对一些特殊值做特殊处理
function parseDataForApp(data) {
  if (!data && data !== 0) return null;
  if (Utils.isPlainObject(data)) {
    return JSON.stringify(data);
  }
  if (Utils.isNumber(data)) return String(data);
  if (Utils.isString(data)) return data;
  return null;
}

// 旧版本WebViewJavascriptBridge初始化
function initIOSBridgeOld(cb) {
  // 创建了一个connectWebViewJavascriptBridge方法，该方法名是固定的
  function connectWebViewJavascriptBridge(callback) {
    if (window.WebViewJavascriptBridge) {
      callback(window.WebViewJavascriptBridge);
    } else {
      document.addEventListener(
        'WebViewJavascriptBridgeReady',
        function () {
          callback(window.WebViewJavascriptBridge);
        },
        false
      );
    }
  }

  // 调用connectWebViewJavascriptBridge方法
  connectWebViewJavascriptBridge(function (bridge) {
    console.log('ios bridge init...');
    bridge.init(function (message, responseCallback) {
      // do something
      responseCallback(message);
    });
    cb(bridge);
  });
}

// 新版本WebViewJavascriptBridge初始化
function initIOSBridgeNew(cb, failCallbackFn) {
  function setupWebViewJavascriptBridge(callback) {
    if (window.WebViewJavascriptBridge) {
      return callback(window.WebViewJavascriptBridge);
    }
    if (window.WVJBCallbacks) {
      return window.WVJBCallbacks.push(callback);
    }
    window.WVJBCallbacks = [callback];
    var WVJBIframe = document.createElement('iframe');
    WVJBIframe.style.display = 'none';
    WVJBIframe.src = 'https://__bridge_loaded__';
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(function () {
      document.documentElement.removeChild(WVJBIframe);
    }, 0);
  }

  var BridgeStatus = {
    LOADING: 1,
    READY: 2,
    FAIL: 3,
  };
  var bridgeStatus = BridgeStatus.LOADING;
  setupWebViewJavascriptBridge(function (bridge) {
    if (bridgeStatus === BridgeStatus.LOADING) {
      bridgeStatus = BridgeStatus.READY;
      cb(bridge);
    }
    /* Initialize your app here */
    /* bridge.registerHandler('JS Echo', function(data, responseCallback) {
      console.log("JS Echo called with:", data)
      responseCallback(data)
    })
    bridge.callHandler('ObjC Echo', {'key':'value'}, function responseCallback(responseData) {
      console.log("JS received response:", responseData)
    })
    */
  });

  // 初始化超时检测
  // iOS端有些WebView未提供iosWebViewBridge支持，初始化回调函数永远无法调用，故增加超时检测
  // GA用户计时工具统计数据显示，用户sdk准备时间最长不到600ms，暂设为1500ms，后期根据数据再做修正
  var bridgeInitTime = 1500;
  setTimeout(function () {
    if (bridgeStatus === BridgeStatus.LOADING) {
      bridgeStatus = BridgeStatus.FAIL;
      failCallbackFn('bridge init timeout');
    }
  }, bridgeInitTime);
}

function initIOSBridge(cb, failCallback) {
  var version = Utils.getVersion();
  console.log('start init ios bridge ...');
  if (version > 370) {
    initIOSBridgeNew(function (bridge) {
      cb(bridge);
    }, failCallback);
  } else {
    initIOSBridgeOld(function (bridge) {
      cb(bridge);
    });
  }
}

export class JglhSDK extends Jglh {
  constructor(options = {}) {
    super();
    // this.init(options.success, options.fail);
  }
  init(success, fail) {
    const nativeCallee = configs.nativeCallee;
    // const isIOS = ENV.ios;
    // const isJglhWebview = ENV.jglh;
    if (typeof window[nativeCallee.android] === 'object') {
      // console.log('android js init...');
      success('android');
    } else if (ENV.ios && ENV.jglh) {
      initIOSBridge(function (bridge) {
        window[nativeCallee.ios] = bridge;
        success('ios');
      }, fail);
    } else {
      fail();
    }
  }
  // canIUse(methodName) {
  //   if (window[configs.nativeCallee.android]) {
  //     return !!window[configs.nativeCallee.android][methodName];
  //   } else if (window[configs.nativeCallee.ios]) {
  //     // TODO: 目前，iOS判断当前版本是否支持此方法只能根据App版本号一个一个对比

  //     return true;
  //   } else {
  //     return false;
  //   }
  // }
  // 调用native提供的方法
  /**
   * 调用app提供的原生方法
   * @param {string} method 函数名
   * @param {object} data 配置数据
   * @param {function} callback 接收函数执行结果的回调函数。早期使用过，后期很少用，目前仅为兼容老版本而保留，未来将废弃
   */
  callNative(method, data, callback) {
    var that = this;
    var iosMethod = method;
    var androidMethod = method;

    // 交广领航早期定义的一些接口，ios端和android端定义的方法名不一样，此处代码为了兼容这种差异，method支持传入一个对象来配置ios和android两端的方法名
    if (!Utils.isString(method)) {
      if (!Utils.isPlainObject(method)) throw new Error('method参数不合法!');
      else if (!Utils.isString(method.android) && !Utils.isString(method.ios))
        throw new Error('method参数不合法!');
      else {
        androidMethod = method.android;
        iosMethod = method.ios;
      }
    }

    if (!Utils.isFunction(callback)) callback = function () {};
    // 与app通信不支持直接传递函数对象，需要结合method名称将data中的函数转成 calling path
    const transedData = transformData(method, data);
    const theData = parseDataForApp(transedData);
    const shouldPassData = !!(data || data === 0);
    if (window[configs.nativeCallee.android]) {
      console.log(
        configs.nativeCallee.android,
        androidMethod,
        typeof theData,
        ':',
        theData
      );
      console.log('shouldPassData:', shouldPassData);
      let result;
      // 一些低版本android系统不支持通过call或apply动态传递参数，曾导致app出现异常，此处做兼容处理
      if (shouldPassData) {
        result = window[configs.nativeCallee.android][androidMethod](theData);
      } else {
        result = window[configs.nativeCallee.android][androidMethod]();
      }
      callback(result);
    } else if (window[configs.nativeCallee.ios]) {
      console.log(
        configs.nativeCallee.ios,
        iosMethod,
        typeof theData,
        ':',
        theData,
        callback
      );
      window[configs.nativeCallee.ios].callHandler(
        iosMethod,
        theData,
        callback
      );
    } else {
      console.log('call ', method, '(', typeof theData, ':', theData, ')');
      callback();
    }
  }
}
