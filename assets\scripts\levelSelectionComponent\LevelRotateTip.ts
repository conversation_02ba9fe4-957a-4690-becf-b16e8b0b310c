// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;
import { AppConstants } from '../utils/constants';
import { ActionUtils } from '../utils/ActionUtils';
import { globalVariables } from '../utils/GlobalVariables';

@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  rotate: cc.Node = null;

  // LIFE-CYCLE CALLBACKS:

  onButtonClick() {
    // 点击后，隐藏当前节点
    cc.tween(this.node).then(ActionUtils.disappearAction(0.5)).start();

    setTimeout(() => {
      this.node.active = false;
    }, 400);
  }

  onLoad() {}

  start() {
    // 设置rotate的动画，-19到19之间来回摆动
    cc.tween(this.rotate)
      .repeatForever(cc.tween().to(0.5, { angle: -19 }).to(0.5, { angle: 19 }))
      .start();
  }

  // update (dt) {}
}
