// Eazax-CCC 镂空(圆) 1.0.1.20200604
// https://gitee.com/ifaswind/eazax-ccc/blob/master/resources/effects/eazax-hollowout-circle.effect

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        size: { value: [500.0, 500.0], editor: { tooltip: '节点尺寸' } }
        center: { value: [0.5, 0.5], editor: { tooltip: '中心点 (左上角为原点)' } }
        radius: { value: 0.5, editor: { tooltip: '半径 (目标宽度 / 节点宽度)' } }
        feather: { value: 0.01, editor: { tooltip: '边缘虚化宽度' } }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>

  in vec3 a_position;
  in vec2 a_uv0;
  in vec4 a_color;

  out vec2 v_uv0;
  out vec4 v_color;

  void main () {
    gl_Position = cc_matViewProj * vec4(a_position, 1);
    v_uv0 = a_uv0;
    v_color = a_color;
  }
}%


CCProgram fs %{
  precision highp float;

  in vec2 v_uv0;
  in vec4 v_color;

  uniform sampler2D texture;

  uniform Properties {
    vec2 center;
    vec2 size;
    float radius;
    float feather;
  };

  void main () {
    vec4 color = v_color;
    color *= texture(texture, v_uv0);
    
    float ratio = size.x / size.y;
    float dis = distance(vec2(v_uv0.x, v_uv0.y / ratio), vec2(center.x, center.y / ratio));
    color.a = smoothstep(radius - feather, radius, dis) * color.a;

    color.a *= v_color.a;
    gl_FragColor = color;
  }
}%
