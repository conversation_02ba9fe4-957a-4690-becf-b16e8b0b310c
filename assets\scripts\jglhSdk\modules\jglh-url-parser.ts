
/**
 * lh_wvc 是交广领航App从原生界面打开webview时的配置参数
 * 由于各种原因，交广领航app中webview可供js动态控制的特性不多，很多配置需要在app打开webview之前先确定好
 * 在此历史背景下，lh_wvc被设计了出来，为了避免参数混淆，hl_wvc参数会被转成base64编码
 * 弊端是人眼无法直接识别，需要解码，有时可能会比较长
 */
const PARAM_NAME = 'lh_wvc';

function getDefaultOptions() {
  return  {
    titleBar: true,
    title: '',
    shareButton: true,
    autoUpdateTitle: true,
  
    renderer: 'x5',
    pulldownRefresh: false,
    progressBar: true,
    requireSignIn: false,

    sbarColor: '',
    sbarBgColor: '',
  }
}

/**
 * 解析url
 * @param {*} url 
 * @param {*} ignoreDefaults 
 */
function parseJglhURL(url, ignoreDefaults = false) {
  var settings = getDefaultOptions();
  settings.url = url;
  try {
    var webviewURL = new URL(url);
    var config = webviewURL.searchParams.get(PARAM_NAME);
    // settings.url = url;
    if (config) {
      var options = JSON.parse(decodeURIComponent(atob(decodeURIComponent(config))));
      webviewURL.searchParams.delete(PARAM_NAME);
      settings = Object.assign({}, settings, options);
      if (ignoreDefaults) return { url: webviewURL.href, ...options };
    } else if(ignoreDefaults) {
      return { url };
    }
    settings.url = webviewURL.href;
    return settings;
   } catch(e) {
     return ignoreDefaults ? {} : settings;
   }
}

/**
 * 过滤配置对象，去除不支持的参数
 * @param {object} options 
 * @param {boolean} ignoreDefaults 是否忽略默认值，当参数值和默认值一样时忽略此参数
 */
function filterOptions(options = {}, ignoreDefaults = false) {
  const defaultOptions = getDefaultOptions();
  // 过滤
  return Object.keys(options).filter(key => {
    return key in defaultOptions;
  }).reduce((prev, key) => {
    if (ignoreDefaults && options[key] === defaultOptions[key]) return prev;
    prev[key] = options[key];
    return prev;
  }, {})
}

/**
 * 根据配置创建交广领航url
 * @param {*} url 地址
 * @param {*} options 配置对象
 */
function createJglhURL(url, options) {
  try {
    const webviewURL = new URL(url);
    webviewURL.searchParams.delete(PARAM_NAME);
    const filteredOptions = filterOptions(options, true);
    if (!Object.keys(filteredOptions).length) return url;
    const base64Text = btoa(encodeURIComponent(JSON.stringify(filteredOptions)));
    webviewURL.searchParams.append(PARAM_NAME, base64Text);
    return webviewURL.href;
   } catch(err) {
     return url;
   }
}

export default { parseJglhURL, createJglhURL };
