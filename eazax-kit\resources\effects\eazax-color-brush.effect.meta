{"ver": "1.0.27", "uuid": "61c2e552-6ea2-48f5-96e4-496ce841dc35", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nvarying vec3 v_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\nattribute float a_dist;\nvarying float v_dist;\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * cc_matWorld * pos;\n  v_position = a_position;\n  v_color = a_color;\n  v_dist = a_dist;\n  gl_Position = pos;\n}", "frag": "\n#if CC_SUPPORT_standard_derivatives\n  #extension GL_OES_standard_derivatives : enable\n#endif\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec3 v_position;\nvarying vec4 v_color;\nvarying float v_dist;\nuniform vec2 size;\nvec3 gradient(float f) {\n  vec3 a = fract(f + vec3(1.0, 2.0 / 3.0, 1.0 / 3.0));\n  vec3 b = abs(a * 5.5 - 3.0);\n  return clamp(b - 1.0, 0.0, 1.0);\n}\nvoid main () {\n  vec2 pos = v_position.xy / size;\n  float d = distance(pos, vec2(1.0, 1.0));\n  vec4 o = vec4(gradient(d), 1.0);\n  ALPHA_TEST(o);\n  #if CC_SUPPORT_standard_derivatives\n    float aa = fwidth(v_dist);\n  #else\n    float aa = 0.05;\n  #endif\n  float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);\n  o.rgb *= o.a;\n  o *= alpha;\n  gl_FragColor = o;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nout vec3 v_position;\nin vec4 a_color;\nvarying vec4 v_color;\nin float a_dist;\nout float v_dist;\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * cc_matWorld * pos;\n  v_position = a_position;\n  v_color = a_color;\n  v_dist = a_dist;\n  gl_Position = pos;\n}", "frag": "\n#if CC_SUPPORT_standard_derivatives\n  #extension GL_OES_standard_derivatives : enable\n#endif\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec3 v_position;\nin vec4 v_color;\nin float v_dist;\nuniform Properties {\n  vec2 size;\n};\nvec3 gradient(float f) {\n  vec3 a = fract(f + vec3(1.0, 2.0 / 3.0, 1.0 / 3.0));\n  vec3 b = abs(a * 5.5 - 3.0);\n  return clamp(b - 1.0, 0.0, 1.0);\n}\nvoid main () {\n  vec2 pos = v_position.xy / size;\n  float d = distance(pos, vec2(1.0, 1.0));\n  vec4 o = vec4(gradient(d), 1.0);\n  ALPHA_TEST(o);\n  #if CC_SUPPORT_standard_derivatives\n    float aa = fwidth(v_dist);\n  #else\n    float aa = 0.05;\n  #endif\n  float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);\n  o.rgb *= o.a;\n  o *= alpha;\n  gl_FragColor = o;\n}"}}], "subMetas": {}}