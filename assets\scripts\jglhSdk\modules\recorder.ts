export default {

  /**
   * 开始录音
   * @alias module:native.startRecord
   */
  startRecord: function () {
    this.callNative('startRecord')
  },

  /**
   * 停止录音，回调函数success返回录音对象
   * @alias module:native.stopRecord
   * @param {Object} option
   * @param {recordCallback} option.success - 成功回调函数
   */
  stopRecord: function (option) {
    var that = this;
    // var settings = that.fnFilter(option);
    that.callNative('stopRecord', option)
  },

  /**
   * 播放录音
   * @alias module:native.playVoice
   * @param {object} option
   * @param {string} option.localId  - 播放的音频id
   */
  playVoice: function (option) {
    this.callNative('playRecord', option)
  },

  /**
   * 播放录音
   * @alias module:native.stopVoice
   * @param {object} option
   * @param {string} option.localId  - 播放的音频id
   */
  stopVoice: function (option) {
    this.callNative('stopPlayRecord', option)
  },
  /**
   * 上传语音接口
   * @alias module:native.uploadVoice
   * @param {Object} options
   * @param {recordCallback} option.success - 成功回调函数
   */
  uploadVoice: function (options) {
    var that = this;
    // var settings = that.fnFilter(options);
    // window.upLoadImageSuccess=option.success;
    that.callNative('uploadRecord', options)
  },
	startDiscriminateVoice: function(option) { // 开始识别
		// var settings = this.fnFilter(option, 'startDiscriminateVoice');
		this.callNative('startDiscriminateVoice', option);
	},
	watchDiscriminateVoice: function(option) { // 自动停止识别
		// var settings = this.fnFilter(option, 'watchDiscriminateVoice');
		this.callNative('watchDiscriminateVoice', option);
	},
	endDiscriminateVoice: function(option) { // 主动停止识别
		// var settings = this.fnFilter(option, 'endDiscriminateVoice');
		this.callNative('endDiscriminateVoice', option);
	}
}
