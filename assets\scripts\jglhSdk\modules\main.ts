import configs from '../config';
import Utils from '../utils';
import {
  Jglh
} from '../core';

// 创建供native可调用的回调函数
function createCallbackForNative (fn, fnName) {
  var poolName = '_appjs_callbacks';
  var pool = window[poolName];
  if (!pool) pool = window[poolName] = {};
  const randomName = String(Math.random()).replace(/^0\./g, '');
  var name = `_callback_${fnName}_${randomName}`;
  if (name in pool) {
    createCallbackForNative(fn);
    return;
  }

  pool[name] = function () {
    fn.apply(Jglh.prototype, arguments);
    // removeCallbackForNative(name); // 执行后删除函数
  }
  return `window.${poolName}.${name}`;
  // return 'window.' + poolName + '.' + name;
}

// 移除回调函数
function removeCallbackForNative (name) {
  var poolName = '_appjs_callbacks';
  var pool = window[poolName];
  if (name in pool) {
    pool[name] = null;
    delete pool[name];
  }
}

// 处理参数中的函数
function fnFilter (method, option) {
  for (var value in option) {
    if (Utils.isFunction(option[value])) {
      option[value] = createCallbackForNative(option[value], `${method}_${value}`);
    }
  }
  return option;
}

// 将数据中的函数转换为字符串
function transformData(methodName, data) {
  if (Utils.isString(methodName) && Utils.isPlainObject(data)) return fnFilter(methodName, data);
  return data;
}

// 数据兼容处理：由于app端数据类型局限，此处需要对一些特殊值做特殊处理
function parseData(data) {
  if (!data && data !== 0) return null;
  if (Utils.isPlainObject(data)) {
    return JSON.stringify(data);
  }
  if (Utils.isNumber(data)) return String(data);
  if (Utils.isString(data)) return data;
  return null;
}

// 旧版本WebViewJavascriptBridge初始化
function initIOSBridgeOld(cb) {
  // 创建了一个connectWebViewJavascriptBridge方法，该方法名是固定的
  function connectWebViewJavascriptBridge(callback) {
    if (window.WebViewJavascriptBridge) {
      callback(window.WebViewJavascriptBridge)
    } else {
      document.addEventListener('WebViewJavascriptBridgeReady', function () {
        callback(window.WebViewJavascriptBridge)
      }, false)
    }
  }

  // 调用connectWebViewJavascriptBridge方法
  connectWebViewJavascriptBridge(function (bridge) {
    console.log('ios bridge init...')
    bridge.init(function (message, responseCallback) {
      // do something
      responseCallback(message)
    })
    cb(bridge);
  })
}

// 新版本WebViewJavascriptBridge初始化
function initIOSBridgeNew(cb, failCallbackFn) {
  function setupWebViewJavascriptBridge(callback) {
    if (window.WebViewJavascriptBridge) {
      return callback(window.WebViewJavascriptBridge);
    }
    if (window.WVJBCallbacks) {
      return window.WVJBCallbacks.push(callback);
    }
    window.WVJBCallbacks = [callback];
    var WVJBIframe = document.createElement('iframe');
    WVJBIframe.style.display = 'none';
    WVJBIframe.src = 'https://__bridge_loaded__';
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(function () {
      document.documentElement.removeChild(WVJBIframe)
    }, 0)
  }

  var BridgeStatus = {
    LOADING: 1,
    READY: 2,
    FAIL: 3,
  }
  var bridgeStatus = BridgeStatus.LOADING;
  setupWebViewJavascriptBridge(function (bridge) {
    if (bridgeStatus === BridgeStatus.LOADING) {
      bridgeStatus = BridgeStatus.READY;
      cb(bridge);
    }
    /* Initialize your app here */
    /* bridge.registerHandler('JS Echo', function(data, responseCallback) {
      console.log("JS Echo called with:", data)
      responseCallback(data)
    })
    bridge.callHandler('ObjC Echo', {'key':'value'}, function responseCallback(responseData) {
      console.log("JS received response:", responseData)
    })
    */
  })

  // 初始化超时检测
  // iOS端有些WebView未提供iosWebViewBridge支持，初始化回调函数永远无法调用，故增加超时检测
  // GA用户计时工具统计数据显示，用户sdk准备时间最长不到600ms，暂设为1500ms，后期根据数据再做修正
  var bridgeInitTime = 1500;
  setTimeout(function () {
    if (bridgeStatus === BridgeStatus.LOADING) {
      bridgeStatus = BridgeStatus.FAIL;
      failCallbackFn('bridge init timeout');
    }
  }, bridgeInitTime);
}

function initIOSBridge(cb, failCallback) {
  var version = Utils.getVersion();
  console.log('start init ios bridge ...');
  if (version > 370) {
    initIOSBridgeNew(function (bridge) {
      cb(bridge);
    }, failCallback);
  } else {
    initIOSBridgeOld(function (bridge) {
      cb(bridge);
    })
  }
}

export default {
  init: function (success, fail) {
    const nativeCallee = configs.nativeCallee;
    const mayIOS = 'ontouchstart' in document && /mobile\/\w+$/i.test(navigator.userAgent);
    const isIOS = /iphone|ipad|ipod/i.test(navigator.userAgent);
    const isJglhWebview = /jgrm1041/i.test(navigator.userAgent);
    if (typeof window[nativeCallee.android] === 'object') {
      // console.log('android js init...');
      success('android');
    } else if (isJglhWebview && isIOS) {
      initIOSBridge(function (bridge) {
        window[nativeCallee.ios] = bridge;
        success('ios');
      }, fail);
    } else {
      fail();
    }
  },
  canIUse: function (methodName) {
    if (window[configs.nativeCallee.android]) {
      return !!window[configs.nativeCallee.android][methodName];
    } else if (window[configs.nativeCallee.ios]) {
      // TODO: 判断当前版本是否支持此方法
      return true;
    } else {
      return false;
    }
  },
  // 调用native提供的方法
  callNative: function (method, data, callback) {
    var that = this;
    var iosMethod = method;
    var androidMethod = method;

    if (!Utils.isString(method)) {
      if (!Utils.isPlainObject(method)) throw new Error('method参数不合法!');
      else if (!Utils.isString(method.android) && !Utils.isString(method.ios)) throw new Error('method参数不合法!');
      else {
        androidMethod = method.android;
        iosMethod = method.ios;
      }
    }

    if (!Utils.isFunction(callback)) callback = function () {};
    var data2 = transformData(method, data);
    var theData = parseData(data2);
    // var theData = JSON.stringify(data);
    var passData = !!(data || data === 0);
    if (window[configs.nativeCallee.android]) {
      // 为了兼容低版本android，需通过method()方式调用且不能将method赋给临时变量
      console.log(configs.nativeCallee.android, androidMethod, typeof theData, ':', theData)
      console.log('passData:', passData)
      var result;
      if (passData) {
        result = window[configs.nativeCallee.android][androidMethod](theData);
      } else {
        result = window[configs.nativeCallee.android][androidMethod]();
      }
      callback(result);
    } else if (window[configs.nativeCallee.ios]) {
      console.log(configs.nativeCallee.ios, iosMethod, typeof theData, ':', theData, callback)
      window[configs.nativeCallee.ios].callHandler(iosMethod, theData, callback)
    } else {
      console.log('call ', method, '(', typeof theData, ':', theData, ')');
      callback();
    }
  },

  // // 创建供native可调用的回调函数
  // createCallbackForNative: function (fn, options = { repeatedly: false }) {
  //   const repeatedly = options.repeatedly;
  //   const fnName = options.name || '';
  //   var poolName = '_appjs_callbacks';
  //   var pool = window[poolName];
  //   var that = this;
  //   if (!pool) pool = window[poolName] = {};
  //   const randomName = String(Math.random()).replace(/^0\./g, '');
  //   var name = `_callback_${fnName}_${randomName}`;
  //   if (name in pool) {
  //     that.createCallbackForNative(fn);
  //     return;
  //   }

  //   pool[name] = function () {
  //     console.log('arguments:', arguments)
  //     fn.apply(Jglh.prototype, arguments);
  //     if (!repeatedly) that.removeCallbackForNative(name)
  //   }

  //   return 'window.' + poolName + '.' + name;
  // },

  // // 移除回调函数
  // removeCallbackForNative: function (name) {
  //   var poolName = '_appjs_callbacks';
  //   var pool = window[poolName];
  //   if (name in pool) {
  //     pool[name] = null;
  //     delete pool[name];
  //   }
  // },
  // // 处理参数中的函数
  // fnFilter: function (option, repeatedly) {
  //   var that = this;
  //   for (var o in option) {
  //     if (Utils.isFunction(option[o])) {
  //       option[o] = that.createCallbackForNative(option[o], {
  //         name: o,
  //         repeatedly: repeatedly,
  //       }); 
  //     }
  //   }
  //   return option;
  // },
}
