const express = require('express');
const cors = require('cors'); // 引入 CORS 中间件
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();

// 转发请求到目标服务器
// 静态文件服务（Cocos Creator 构建后的文件）
// app.use(express.static('build'));

// 设置CORS配置
const corsOptions = {
  origin: 'http://192.168.3.153:7456', // 允许的源，必须与请求的源匹配
  credentials: true, // 允许请求带上凭证
};

app.use(cors(corsOptions)); // 使用CORS配置

// 代理配置
app.use(
  '/api',
  createProxyMiddleware({
    target: 'https://dev.jgrm.net', // 目标服务器地址
    changeOrigin: true, // 跨域
    secure: false, // 不验证SSL证书
    pathRewrite: {
      '^/api': '', // 重写路径（可选）
    },
    // logLevel: 'debug',  // 日志级别，用于调试
    // onProxyRes(proxyRes, req, res) {
    //   // 设置自定义的 CORS 头部
    //   proxyRes.headers['Access-Control-Allow-Origin'] = 'http://192.168.3.153:7456'; // 允许的来源
    //   proxyRes.headers['Access-Control-Allow-Credentials'] = 'true'; // 允许凭证
    // },
  })
);

// 启动服务器
const port = 7457;
app.listen(port, () => {
  console.log(`Server is running on http://192.168.3.153:${port}`);
});
