// import JglhSDK from '@/lib/jglh-sdk/jglh-sdk';
import JglhSDK from '../jglhSdk/index';
import {
  isInWeApp,
  isInWeixin,
  isInJGLH,
  isProduction,
  isInJglh,
  isInAliApp,
  isIOS,
  isAndroid,
} from './generalUtil';
import { isValidURL } from './generalUtil';
import { saveTokenToStorage, getTokenFromStorage } from './storage';
import JglhWeApp from './JglhWeApp';
import {
  requestWeixinAuth,
  setWeixinShareInfo,
  hideWeixinMenus,
  requestWeixinAuthWithUid,
} from './weixin';

const isInApp = isInJGLH;
const isInIOS = isIOS;
const isInAndroid = isAndroid;

const jglh: any = new JglhSDK();
/**
 * 调起交广领航的登录界面
 * 曾经采用 先尝试从客户端获取session, 若得到session认为是已登录状态，不再重复登录
 * 但后期服务器接口改造，此判断方式已不可靠，故调用此方法进行强制登录操作
 * @params {Object} options 配置参数
 * @param {Boolean} options.force 是否强制登录, 默认为true
 * @returns {Promise}
 */
export function appLogin() {
  return new Promise((resolve, reject) => {
    jglh.login(uid => {
      console.log('login success ...', uid);
      jglh.getSession(session => {
        setSession(session);
        resolve(session);
      });
    });
  });
}

/**
 * 统一登录方法
 * @param {object|string} options: 可能是一个对象，也可能是一个url
 */
export function login(options) {
  if (isInJglh) {
    return appLogin();
  } else if (isInWeApp) {
    // 小程序处理
    return new Promise((resolve, reject) => {
      JglhWeApp.login();
    });
  } else if (isInWeixin) {
    const nextPage = isValidURL(options) ? options : location.href;
    return Promise.resolve().then(function () {
      requestWeixinAuth(nextPage);
      return true;
    });
  } else {
    // TDOO: 网页登录
    console.error('当前环境不支持网页登录！');
  }
}

function setSession(session) {
  /**
   *  或不应判断session，否则若登录失效，session依然有效
   *  但有时webview会被系统杀死（android端表现为webview顶部出现横向黑条，iOS端表现为webview假死），导致getSession拿不到数据，但此时用户是登录状态，此时将session清除会导致
   *  用户可能无法正常进行后续操作
   */
  if (!session) session = '';
  /* if (!session) {
    console.warn('!session:', session);
    return;
  } */
  console.log('session:', typeof session, ',', session);
  // storage.setItem(SESSION_NAME, session);
  saveTokenToStorage(session);
}

export function syncSession() {
  return new Promise((resolve, reject) => {
    if (isInApp) {
      jglh.getSession(sessionId => {
        setSession(sessionId);
        resolve(sessionId);
      });
    } else {
      resolve(true);
    }
  });
}

export function getSession() {
  return getTokenFromStorage();
}

export function isLoggedIn() {
  return !!getSession();
}

export function checkLoggedIn() {
  return new Promise((resolve, reject) => {
    jglh.getSession(session => {
      session ? resolve(true) : reject();
    });
  });
}

export function disablePullDownRefresh() {
  jglh.setPulldownRefreshFlag(false);
}

export { requestWeixinAuthWithUid };

export function setTitle(title) {
  if (isInApp) {
    /**
     * jglh-3.9.3及之前
     * SPA应用返回会触发android webview的 onReceivedTitle 回调方法，Android端会在回调中更新titlebar的标题
     * 若android没有做防抖处理，可能会出现标题闪烁现象
     * 暂时采用将title设为空字符串来降低视觉上的闪烁效果
     * 2018年7月30日15:52:09，与android协商，将在下个版本：3.9.4中加入150ms防抖处理
     */
    // document.title = ' ';
    return jglh.setTitle(title);
  } else {
    document.title = title;
  }
}

/**
 * 将url解析为pushWebView方法的参数对象
 * @param {string} url url地址
 */
export function parseJglhURL(...args) {
  return jglh.parseJglhURL(...args);
}

/**
 * 获取设备信息
 */
export function getSystemInfo() {
  return new Promise((resolve, reject) => {
    if (canIUseJglh('getSystemInfo')) {
      jglh.getSystemInfo({
        success: function (res) {
          resolve(res);
        },
        fail(err) {
          reject(err);
        },
      });
    } else {
      reject('');
    }
  });
}

/**
 * 获取设备id
 */
export function getDeviceId() {
  return new Promise((resolve, reject) => {
    if (canIUseJglh('getSystemInfo')) {
      jglh.getSystemInfo({
        success: function (res) {
          resolve(res.id);
        },
        fail(err) {
          reject(err);
        },
      });
    } else {
      reject('');
    }
  });
}

/**
 * 禁用android端的webview进度条
 * @deprecated App端未实现
 */
export function disableProcessBar() {
  jglh.setProcessBarStatus(0);
}
/**
 * 禁用android端的webview自动更新标题栏
 * @deprecated App端未实现
 */
export function disableAutoUpdateTitle() {
  jglh.setAutoUpdateTitleStatus(0);
}

export function pushWebView(options) {
  jglh.pushWebView(options);
}

export function pushNativeView(options) {
  jglh.pushNativeView(options);
}

export function popWebView(number) {
  // 截止iOS 3.9.3 Bug：popWebView未按要求实现，只能通过传入1实现后退且退到最后一步退出webview，不能直接退出webview
  if (isInIOS) {
    jglh.popWebView(1);
  } else {
    jglh.popWebView(number);
  }
}

export function getVersion() {
  return jglh.getVersion();
}

export function getVersionName() {
  if (isInApp) {
    return `jglh-${isInAndroid ? 'android' : 'ios'}/` + jglh.getVersion();
  } else if (isInWeixin) {
    try {
      return /.*\s(MicroMessenger\/[\d.]+)/i.exec(navigator.userAgent)[1];
    } catch (e) {
      return 'MicroMessenger/unknow';
    }
  } else return 0;
}

export function getUserID() {
  return new Promise((resolve, reject) => {
    jglh.getUID(uid => {
      uid ? resolve(uid) : resolve('anonymous');
    });
  });
}

/**
 * 判断当前环境是否支持指定的方法
 * @param {*} schema
 */
export function canIUseJglh(schema) {
  return jglh && jglh.canIUse(schema);
}

export function couldUseNative() {
  return jglh.couldUseNative();
}

export function initNativeJs(...args) {
  jglh.init(...args);
}

/**
 * 设置分享信息
 * 如果在交广领航内，调用app的分享接口
 * 如果在微信环境下，调用微信js-sdk的分享接口
 * @param {*} options
 */
export function setShareConfig(options) {
  if (options && options.title && options.title.length >= 25) {
    console.warn(
      `分享标题长度：${options.title.length}，微信分享标题字符长度大于25可能无法保证在所有手机上都完整显示`
    );
  }
  if (isInWeixin) {
    setWeixinShareInfo(options);
    // 小程序内需要向小程序传递页面信息
    if (isInWeApp) {
      window.wx.miniProgram.postMessage({ data: options });
      // window.wx.miniProgram.navigateTo({ url: `/pages/web/index?url=${encodeURIComponent('about:blank')}` });
      // window.wx.miniProgram.navigateBack()
    }
  } else {
    jglh.setShareConfig(options);
  }
}

/**
 * 调起分享界面，仅在交广领航内有效
 * @param {object} options 同setShareConfig
 */
export function share(options) {
  jglh.share(options);
}

/**
 * 初始化app配置信息
 */
export function initAppConfig(shareInfo) {
  const shareConfig = Object.assign(
    {
      title: '交广领航车生活平台',
      desc: '交广领航车生活平台，严选商家、定期抽检、媒体监督，为用户找放心商家。',
      link: 'http://www.jgrm.net/mobile/jglh.html', // 必须与公众号JS安全域名一致
      imgUrl: 'https://img.jgrm.net/Fi6QQSe98E9zHC9EKCKBiTFiRNFo_xlogo',
    },
    shareInfo
  );
  setShareConfig(shareConfig);
  if (isInWeixin && isProduction) {
    hideWeixinMenus([
      // 'menuItem:share:appMessage',
      // 'menuItem:share:timeline',
      'menuItem:share:qq',
      'menuItem:share:weiboApp',
      // 'menuItem:favorite',
      'menuItem:share:facebook',
      'menuItem:share:QZone',

      'menuItem:editTag',
      'menuItem:delete',
      'menuItem:copyUrl',
      'menuItem:originPage',
      'menuItem:readMode',
      'menuItem:openWithQQBrowser',
      'menuItem:openWithSafari',
      'menuItem:share:email',
      'menuItem:share:brand',
    ]);
  }
}

function wrapPromise(fn) {
  return new Promise((resolve, reject) => {
    try {
      fn.call(this, resolve, reject);
    } catch (err) {
      reject(err);
    }
  });
}

/**
 * @description: app中打开小程序
 * @return {*}
 */
export function openMiniProgram(params): Promise<void> {
  let path = params.path;
  let id = params.id || params.ghid;
  let mpEnv = params.env || 'release';
  // var appId = params.appid;
  return new Promise<void>((resolve, reject) => {
    if (isInApp) {
      // jglh环境需要 ghid，小程序环境需要 appid
      jglh.navigateToMiniProgram({
        userName: id, // 要打开的小程序 原始id
        path: path, // 跳转路径如'page/index/index?id=123',
        envVersion: mpEnv, // develop|trial|release
        success: function (res) {
          // popWebView(0);
          resolve();
        },
        fail: function (err) {
          reject(err);
        },
      });
    } else if (isInWeApp) {
      // 微信webview环境下，需要通过微信标签，点击跳转小程序
      window.wx.miniProgram.navigateTo({
        url: path,
        success: function (res) {
          resolve();
        },
        fail: function (err) {
          console.error(err);
          reject(err);
        },
      });
    } else {
      // 永久小程序URL Scheme 规则有变，暂不适用 公告地址：https://developers.weixin.qq.com/community/develop/doc/000aeab88a4ea0c5c89d81fde5b801
      reject('非交广领航、微信环境调用');
    }
  });
}
