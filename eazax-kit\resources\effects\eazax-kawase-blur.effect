// <PERSON>wase 模糊（Kawase Blur）
// 20211208
// https://gitee.com/ifaswind/eazax-ccc/blob/master/resources/effects/eazax-kawase-blur.effect

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        resolution: { value: [500, 500] }
        offset: { value: 1.0, range: [0.0, 10.0] }
        texture: { value: white }
        alphaThreshold: { value: 0.5 }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>

  in vec3 a_position;
  in vec4 a_color;
  out vec4 v_color;

  #if USE_TEXTURE
  in vec2 a_uv0;
  out vec2 v_uv0;
  #endif

  void main () {
    vec4 pos = vec4(a_position, 1);

    #if CC_USE_MODEL
    pos = cc_matViewProj * cc_matWorld * pos;
    #else
    pos = cc_matViewProj * pos;
    #endif

    #if USE_TEXTURE
    v_uv0 = a_uv0;
    #endif

    v_color = a_color;

    gl_Position = pos;
  }
}%


CCProgram fs %{
  precision highp float;

  #include <alpha-test>
  #include <texture>

  in vec4 v_color;

  #if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D texture;
  #endif

  uniform Properties {
    vec2 resolution;
    float offset;
  };

  #if USE_TEXTURE
  // 模糊函数
  vec4 kawaseBlur(sampler2D tex, vec2 uv, vec2 texelSize, float offset) {
		vec4 o = vec4(0);
		o += texture2D(tex, uv + vec2(offset + 0.5, offset + 0.5) * texelSize); 
		o += texture2D(tex, uv + vec2(-offset - 0.5, offset + 0.5) * texelSize); 
		o += texture2D(tex, uv + vec2(-offset - 0.5, -offset - 0.5) * texelSize); 
		o += texture2D(tex, uv + vec2(offset + 0.5, -offset - 0.5) * texelSize); 
		return o * 0.25;
	}
  #endif

  void main () {
    vec4 o = vec4(1, 1, 1, 1);

    #if USE_TEXTURE
    o *= kawaseBlur(texture, v_uv0, 1.0 / resolution, offset);
    #endif

    o *= v_color;

    ALPHA_TEST(o);

    #if USE_BGRA
      gl_FragColor = o.bgra;
    #else
      gl_FragColor = o.rgba;
    #endif
  }
}%
