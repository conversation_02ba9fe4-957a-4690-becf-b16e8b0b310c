export default {
  /**
   * 打开位置
   * @param {object} options	参数对象
   * @param {number} options.latitude	目标地纬度
   * @param {number} options.longitude	目标地经度
   * @param {number} options.scale	缩放比例，默认18
   * @param {string} options.name	目标地名称
   * @param {string} options.address	目标地详细地址
   * @param {function} options.success 接口调用成功的回调函数
   * @param {function} options.fail	接口调用失败的回调函数
   * @param {function} options.complete	接口调用结束的回调函数（调用成功、失败都会执行）
   */
  openLocation: function (options) {
    this.callNative('openLocation', options)
  },
  /**
   * 成功回调
   * @callback chooseLocationSuccessCallback
   * @param {object} res 成功回调数据
   * @param {string} res.name - 位置名称
   * @param {string} res.address - 详细地址
   * @param {number} res.latitude 选定视频的时间长度
   * @param {number} res.longitude 选定视频的数据量大小
   */
  /**
   * 选择位置
   * @param {object} options	是	参数对象
   * @param {number} options.latitude	否	目标地纬度
   * @param {number} options.longitude	否	目标地经度
   * @param {chooseLocationSuccessCallback} options.success 否	接口调用成功的回调函数
   * @param {function} options.fail	否	接口调用失败的回调函数
   * @param {function} options.complete	否	接口调用结束的回调函数（调用成功、失败都会执行）
   */
  chooseLocation: function (options) {
    this.callNative('chooseLocation', options)
  },

  /**
   * 获取位置信息
   * @param {object} options	参数对象
   * @param {string} options.type 默认wgs84	坐标类型 wgs84|gcj02
   * @param {function} options.success 接口调用成功的回调函数
   * @param {function} options.fail	接口调用失败的回调函数
   * @param {function} options.complete	接口调用结束的回调函数（调用成功、失败都会执行）
   */
  getLocation: function (options) {
    this.callNative('getLocation', options)
  }
}
