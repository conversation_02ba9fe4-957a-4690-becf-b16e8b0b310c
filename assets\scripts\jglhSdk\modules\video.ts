import jglhUrlParser from "./jglh-url-parser"

export default {
  /**
   * 失败回调
   * @callback failCallback
   * @param {string} err 失败回调消息
   */
  /**
   * 成功回调
   * @callback chooseVideoSuccessCallback
   * @param {object} res 成功回调数据
   * @param {string} res.rid - 选定视频的七牛云资源id
   * @param {string} res.path - 选定视频的临时文件路径 (本地路径)
   * @param {number} res.duration 选定视频的时间长度
   * @param {number} res.size 选定视频的数据量大小
   * @param {number} res.height 返回选定视频的高度
   * @param {number} res.width 返回选定视频的宽度
   */

  /**
   * 选择视频
   * @param {object} options	选择视频
   * @param {string} options.sourceType album|camera 因app端反馈实现较复杂，改为string，同时只能支持一个
   * @param {boolean} options.compressed 是否压缩所选择的视频文件 默认true
   * @param {number} options.minDuration 拍摄视频最短拍摄时间，单位秒，默认3
   * @param {number} options.maxDuration 拍摄视频最长拍摄时间，单位秒，默认15
   * @param {string} options.camera 摄像头类型 back|front，默认back
   * @param {chooseVideoSuccessCallback} options.success 接口调用成功的回调函数
   * @param {failCallback} options.fail	接口调用失败的回调函数
   * @param {function} options.complete	接口调用结束的回调函数（调用成功、失败都会执行）
   * @ 
   */
  chooseVideo: function (options) {
    this.callNative('chooseVideo', options)
  },
  /**
   * 播放视频
   * @param {object} options	选择视频
   * @param {string} options.filePath 视频源的本地路径或远程路径
   * @param {function} options.success 接口调用成功的回调函数
   * @param {failCallback} options.fail	接口调用失败的回调函数
   * @param {function} options.complete	接口调用结束的回调函数（调用成功、失败都会执行）
   */
  playVideo: function (options) {
    this.callNative('playVideo', options)
  },
  /**
   * 成功回调
   * @callback openVideoEditorSuccessCallback
   * @param {object} res 成功回调数据
   * @param {string} res.tempFilePath - 选定视频的临时文件路径 (本地路径)
   * @param {number} res.duration 选定视频的时间长度
   * @param {number} res.size 选定视频的数据量大小
   * @param {number} res.height 返回选定视频的高度
   * @param {number} res.width 返回选定视频的宽度
   */
  /**
   * 编辑视频
   * @param {object} options - 编辑视频
   * @param {string} options.filePath - 视频源的本地路径
   * @param {openVideoEditorSuccessCallback}options.success  - 接口调用成功的回调函数
   * @param {failCallback} options.fail - 接口调用失败的回调函数
   * @param {function} options.complete - 接口调用结束的回调函数（调用成功、失败都会执行）
   */
  openVideoEditor: function (options) {
    this.callNative('openVideoEditor', options)
  },
  /**
   * 成功回调
   * @callback uploadFileSuccessCallback
   * @param {object} res 成功回调数据
   * @param {string} res.data - 选定视频的临时文件路径 (本地路径)
   * @param {number} res.statusCode 选定视频的时间长度
   */
  /**
   * 将本地资源上传到服务器。客户端发起一个 POST 请求，其中 content-type 为 multipart/form-data
  * @param {object} options	参数对象
  * @param {string} url	开发者服务器地址
  * @param {string} filePath	要上传文件资源的路径 (本地路径)
  * @param {string} name	文件对应的 key，开发者在服务端可以通过这个 key 获取文件的二进制内容
  * @param {object} header	HTTP 请求 Header，Header 中不能设置 Referer
  * @param {object} formData	HTTP 请求中其他额外的 form data
  * @param {number} timeout	超时时间，单位为毫秒
  * @param {uploadFileSuccessCallback} success	接口调用成功的回调函数
  * @param {failCallback} fail	接口调用失败的回调函数
  * @param {function} complete	接口调用结束的回调函数（调用成功、失败都会执行）
   */
  uploadFile: function (options) {
    this.callNative('uploadFile', options)
    return {
      abort: this.abortUpload,
      onUploadProgressUpdate: this.onUploadProgressUpdate,
      offUploadProgressUpdate: this.offUploadProgress,
    }
  },
  abortUpload: function () {
    this.callNative('abortUpload')
  },
  onUploadProgressUpdate: function (cb) {
    this.callNative('onUploadProgressUpdate', cb)
  },
  offUploadProgressUpdate: function () {
    this.callNative('offUploadProgressUpdate')
  },
}
